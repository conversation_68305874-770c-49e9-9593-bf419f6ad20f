/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Clock, CheckCircle } from 'lucide-react';
import {
  useOrganizationClients,
  useClientsByInvestmentStatus,
  useOrganizationData,
  useActiveTransactionWindow,
  useCreateInitialWindow,
  useOpenWindows,
  useCloseWindow,
  useCloseWindowWithUpdates,
  useCreateProfitDistribution,
  useHandleQuarterTransitions,
  useCompleteCycle,
  useAdminBalanceSheets,
} from '@/hooks/useApi';
import { useQueryClient } from '@tanstack/react-query';
import {
  transactionApi,
  adminBalanceSheetApi,
  userProfileApi,
  organizationApi,
} from '@/lib/supabase';
import { clientBalanceSheetApi } from '@/lib/api';
import {
  MONTHS,
  QUARTERS,
  TRANSACTION_TYPES,
  PERIOD_TYPES,
  getUsersForMonthlyDistribution,
  getUsersForQuarterlyDistribution,
  INVESTMENT_STATUS_LABELS,
  getQuarterByMonth,
  getMonthPeriodId,
  getQuarterPeriodId,
  getPreviousQuarter,
} from '@/lib/constants';

interface DistributionCalculation {
  mavenProfit: number;
  clientProfit: number;
  purification: number;
  total: number;
}

interface ProfitProps {
  calculateProfitDistribution: (totalProfit: number) => DistributionCalculation;
}

const Profit: React.FC<ProfitProps> = ({ calculateProfitDistribution }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [distributionType, setDistributionType] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState(''); // Can be month or quarter
  const [selectedMember, setSelectedMember] = useState('');
  const [distributionValue, setDistributionValue] = useState('');
  const [windowDuration, setWindowDuration] = useState('4'); // Default 4 days
  const [isClosingWindow, setIsClosingWindow] = useState(false);

  // Phase 2: Initial window opening state
  const [initialWindowType, setInitialWindowType] = useState<
    'monthly' | 'quarterly'
  >('monthly');
  const [initialWindowDuration, setInitialWindowDuration] = useState('7');

  // Phase 6: Profit distribution state
  const [profitAmount, setProfitAmount] = useState('');
  const [profitPeriodType, setProfitPeriodType] = useState<
    'monthly' | 'quarterly'
  >('monthly');

  const [profitWindowDuration, setProfitWindowDuration] = useState('7');

  // Enhanced UI Component for Profit Distribution
  const [quarterlyCapitalData, setQuarterlyCapitalData] = useState<any>(null);

  // Manual month/quarter selection for testing
  const [manualMonth, setManualMonth] = useState<number>(
    new Date().getMonth() + 1
  );
  const [manualYear, setManualYear] = useState<number>(
    new Date().getFullYear()
  );
  const [manualQuarter, setManualQuarter] = useState<string>('q1');

  // Phase 7 & 8: Quarter transitions and cycle completion state
  const [selectedQuarterForTransition, setSelectedQuarterForTransition] =
    useState('');
  const [
    selectedAdminBalanceSheetForCompletion,
    setSelectedAdminBalanceSheetForCompletion,
  ] = useState('');

  // Phase 2: Initial window opening hook
  const createInitialWindowMutation = useCreateInitialWindow();

  // Get eligible users based on distribution type
  const eligibleStatuses =
    distributionType === TRANSACTION_TYPES.PNL_MONTHLY
      ? getUsersForMonthlyDistribution()
      : distributionType === TRANSACTION_TYPES.PNL_QUARTERLY
      ? getUsersForQuarterlyDistribution()
      : [];

  const { data: eligibleUsersData } = useClientsByInvestmentStatus(
    user?.organization_id || '',
    eligibleStatuses
  );

  // Note: We're using manual quarter selection for testing, so no need to fetch available quarters

  // Fetch quarterly capital data when quarter is selected
  const fetchQuarterlyCapitalData = useCallback(async () => {
    try {
      const result =
        await adminBalanceSheetApi.getQuarterlyCapitalForDistribution(
          user?.organization_id || '',
          manualQuarter
        );
      if (result.data) {
        setQuarterlyCapitalData(result.data);
      }
    } catch (error) {
      console.error('Error fetching quarterly capital:', error);
    }
  }, [user?.organization_id, manualQuarter]);

  // Note: Using manual quarter selection for testing

  // Fetch quarterly capital data when quarter is selected
  useEffect(() => {
    if (manualQuarter && profitPeriodType === 'quarterly') {
      fetchQuarterlyCapitalData();
    }
  }, [manualQuarter, profitPeriodType, fetchQuarterlyCapitalData]);

  const { data: allClientsData } = useOrganizationClients(
    user?.organization_id || ''
  );
  const { data: organizationData } = useOrganizationData(
    user?.organization_id || ''
  );
  const { data: activeWindowData } = useActiveTransactionWindow(
    user?.organization_id || ''
  );
  const { data: openWindowsData } = useOpenWindows(user?.organization_id || '');
  const { data: adminBalanceSheetsData } = useAdminBalanceSheets(
    user?.organization_id || ''
  );
  console.log(activeWindowData, openWindowsData);
  // Close window hooks
  const closeWindowMutation = useCloseWindow();
  const closeWindowWithUpdatesMutation = useCloseWindowWithUpdates();

  // Phase 6: Profit distribution hook
  const createProfitDistributionMutation = useCreateProfitDistribution();

  // Phase 7 & 8: Quarter transitions and cycle completion hooks
  const handleQuarterTransitionsMutation = useHandleQuarterTransitions();
  const completeCycleMutation = useCompleteCycle();

  const eligibleUsers = eligibleUsersData?.data || [];
  const allClients = allClientsData?.data || [];
  const organization = organizationData?.data;
  const openWindows = openWindowsData?.data || [];
  const adminBalanceSheets = adminBalanceSheetsData?.data || [];

  // Phase 2: Handle initial window opening
  const handleInitialWindowCreation = async () => {
    if (!user?.organization_id) {
      toast({
        title: 'Error',
        description: 'Organization ID not found',
        variant: 'destructive',
      });
      return;
    }

    try {
      await createInitialWindowMutation.mutateAsync({
        organization_id: user.organization_id,
        period_type: initialWindowType,
        transaction_window_duration: parseInt(initialWindowDuration) || 7,
      });
    } catch (error) {
      console.error('Failed to create initial window:', error);
    }
  };

  // Handle closing a specific window (simple close)
  const handleCloseWindow = async (windowId: string) => {
    try {
      await closeWindowMutation.mutateAsync(windowId);
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  };

  // Phase 5: Handle closing window with complete updates
  const handleCloseWindowWithUpdates = async (windowId: string) => {
    try {
      await closeWindowWithUpdatesMutation.mutateAsync(windowId);
    } catch (error) {
      console.error('Failed to close window with updates:', error);
    }
  };

  // Phase 6: Handle profit distribution creation
  const handleCreateProfitDistribution = async () => {
    if (!user?.organization_id) {
      toast({
        title: 'Error',
        description: 'Organization ID not found',
        variant: 'destructive',
      });
      return;
    }

    if (!profitAmount || parseFloat(profitAmount) <= 0) {
      toast({
        title: 'Error',
        description: 'Please enter a valid profit amount',
        variant: 'destructive',
      });
      return;
    }

    if (profitPeriodType === 'quarterly' && !manualQuarter) {
      toast({
        title: 'Error',
        description: 'Please select a quarter for quarterly distribution',
        variant: 'destructive',
      });
      return;
    }

    try {
      await createProfitDistributionMutation.mutateAsync({
        organization_id: user.organization_id,
        period_type: profitPeriodType,
        total_profit_input: parseFloat(profitAmount),
        quarter_name:
          profitPeriodType === 'quarterly' ? manualQuarter : undefined,
        transaction_window_duration: parseInt(profitWindowDuration),
        // Manual month/year for testing
        manual_month: manualMonth,
        manual_year: manualYear,
      });

      // Reset form
      setProfitAmount('');
      setQuarterlyCapitalData(null);
    } catch (error) {
      console.error('Failed to create profit distribution:', error);
    }
  };

  // Phase 7: Handle quarter transitions
  const handleQuarterTransitions = async () => {
    if (!user?.organization_id) {
      toast({
        title: 'Error',
        description: 'Organization ID not found',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedQuarterForTransition) {
      toast({
        title: 'Error',
        description: 'Please select a quarter for transition',
        variant: 'destructive',
      });
      return;
    }

    try {
      await handleQuarterTransitionsMutation.mutateAsync({
        organizationId: user.organization_id,
        completedQuarter: selectedQuarterForTransition,
      });
      setSelectedQuarterForTransition('');
    } catch (error) {
      console.error('Failed to handle quarter transitions:', error);
    }
  };

  // Phase 8: Complete cycle
  const handleCompleteCycle = async () => {
    if (!user?.organization_id) {
      toast({
        title: 'Error',
        description: 'Organization ID not found',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedAdminBalanceSheetForCompletion) {
      toast({
        title: 'Error',
        description:
          'Please select an admin balance sheet for cycle completion',
        variant: 'destructive',
      });
      return;
    }

    try {
      await completeCycleMutation.mutateAsync({
        organizationId: user.organization_id,
        adminBalanceSheetId: selectedAdminBalanceSheetForCompletion,
      });
      setSelectedAdminBalanceSheetForCompletion('');
    } catch (error) {
      console.error('Failed to complete cycle:', error);
    }
  };

  // Extract active window data

  const activeWindow = activeWindowData?.data;
  const isWindowActive = !!activeWindow;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Profit Distribution</h2>

      {/* Transaction Window Status */}
      <Card
        className={
          isWindowActive ? 'border-green-200 bg-green-50' : 'border-gray-200'
        }
      >
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {isWindowActive ? (
              <>
                <Clock className="h-5 w-5 text-green-600" />
                <span className="text-green-800">
                  Transaction Window Active
                </span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-gray-600" />
                <span className="text-gray-800">
                  No Active Transaction Window
                </span>
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isWindowActive && activeWindow ? (
            <div className="space-y-2">
              <p className="text-sm text-green-700">
                <strong>Start Date:</strong>{' '}
                {new Date(activeWindow.window_start_date).toLocaleDateString()}
              </p>
              <p className="text-sm text-green-700">
                <strong>End Date:</strong>{' '}
                {new Date(activeWindow.window_end_date).toLocaleDateString()}
              </p>
              <p className="text-sm text-green-600">
                Clients can submit investment and withdrawal requests during
                this period.
              </p>
            </div>
          ) : (
            <p className="text-sm text-gray-600">
              No transaction window is currently active. Clients cannot submit
              investment or withdrawal requests.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Phase 2: Initial Window Opening */}
      {adminBalanceSheets.length === 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800">
              Create Initial Transaction Window
            </CardTitle>
            <CardDescription className="text-blue-600">
              Set up the first transaction window for your organization. This
              will create initial balance sheets for all clients.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Window Type</Label>
                <Select
                  value={initialWindowType}
                  onValueChange={(value: 'monthly' | 'quarterly') =>
                    setInitialWindowType(value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select window type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Window Duration (Days)</Label>
                <Input
                  type="number"
                  value={initialWindowDuration}
                  onChange={(e) => setInitialWindowDuration(e.target.value)}
                  placeholder="7"
                  min="1"
                  max="30"
                />
              </div>
            </div>

            <Button
              onClick={handleInitialWindowCreation}
              disabled={createInitialWindowMutation.isPending}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {createInitialWindowMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Initial Window...
                </>
              ) : (
                'Create Initial Window'
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Phase 6: Profit Distribution */}
      <Card className="border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800">
        <CardHeader>
          <CardTitle className="text-green-800 dark:text-green-200">
            Profit Distribution
          </CardTitle>
          <CardDescription className="text-green-600 dark:text-green-400">
            Create profit distribution with capital snapshots and client balance
            sheet generation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Period Type</Label>
              <Select
                value={profitPeriodType}
                onValueChange={(value: 'monthly' | 'quarterly') => {
                  setProfitPeriodType(value);
                  setQuarterlyCapitalData(null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select period type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Manual Month Selection for Testing */}
            <div className="space-y-2">
              <Label>Month (Testing)</Label>
              <Select
                value={manualMonth.toString()}
                onValueChange={(value) => setManualMonth(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                    <SelectItem key={month} value={month.toString()}>
                      M{month} (
                      {new Date(2024, month - 1).toLocaleString('default', {
                        month: 'long',
                      })}
                      )
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Manual Year Selection for Testing */}
            <div className="space-y-2">
              <Label>Year (Testing)</Label>
              <Select
                value={manualYear.toString()}
                onValueChange={(value) => setManualYear(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  {[2024, 2025, 2026].map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Manual Quarter Selection for Testing */}
            <div className="space-y-2">
              <Label>Source Quarter (Testing)</Label>
              <Select value={manualQuarter} onValueChange={setManualQuarter}>
                <SelectTrigger>
                  <SelectValue placeholder="Select quarter" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => `q${i + 1}`).map(
                    (quarter) => (
                      <SelectItem key={quarter} value={quarter}>
                        {quarter.toUpperCase()} (
                        {quarter === 'q1'
                          ? 'Jan-Mar'
                          : quarter === 'q2'
                          ? 'Feb-Apr'
                          : quarter === 'q3'
                          ? 'Mar-May'
                          : quarter === 'q4'
                          ? 'Apr-Jun'
                          : quarter === 'q5'
                          ? 'May-Jul'
                          : quarter === 'q6'
                          ? 'Jun-Aug'
                          : quarter === 'q7'
                          ? 'Jul-Sep'
                          : quarter === 'q8'
                          ? 'Aug-Oct'
                          : quarter === 'q9'
                          ? 'Sep-Nov'
                          : quarter === 'q10'
                          ? 'Oct-Dec'
                          : quarter === 'q11'
                          ? 'Nov-Jan'
                          : 'Dec-Feb'}
                        )
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Source quarter for profit distribution and lineage calculations
              </p>
            </div>
          </div>

          {/* Quarterly capital info display */}
          {profitPeriodType === 'quarterly' &&
            manualQuarter &&
            quarterlyCapitalData && (
              <div className="space-y-2">
                <Label>Quarter Capital Info</Label>
                <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>{manualQuarter.toUpperCase()}</strong>:{' '}
                    {quarterlyCapitalData.quarterlyPositions?.length || 0} users
                    with total capital of ₹
                    {quarterlyCapitalData.totalCapital?.toLocaleString() || 0}
                  </p>
                </div>
              </div>
            )}

          <div className="space-y-2">
            <Label>Profit Amount (68% effective)</Label>
            <Input
              type="number"
              placeholder="Enter profit amount"
              value={profitAmount}
              onChange={(e) => setProfitAmount(e.target.value)}
              min="0"
            />
          </div>

          <div className="space-y-2">
            <Label>Transaction Window Duration (Days)</Label>
            <Input
              type="number"
              placeholder="7"
              value={profitWindowDuration}
              onChange={(e) => setProfitWindowDuration(e.target.value)}
              min="1"
              max="30"
            />
            <p className="text-sm text-muted-foreground">
              Number of days clients can submit investment/withdrawal requests
              after profit distribution
            </p>
          </div>

          <Button
            onClick={handleCreateProfitDistribution}
            disabled={
              createProfitDistributionMutation.isPending ||
              (profitPeriodType === 'quarterly' && !manualQuarter)
            }
            className="w-full bg-green-600 hover:bg-green-700"
          >
            {createProfitDistributionMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Distribution...
              </>
            ) : (
              'Distribute Profit & Open Transaction Window'
            )}
          </Button>

          {/* Enhanced Calculation Preview */}
          {profitAmount !== '' && (
            <Card className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
              <CardHeader>
                <CardTitle className="text-green-800 dark:text-green-200">
                  Calculation Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                {(() => {
                  const amount = parseFloat(profitAmount || '0');
                  const purificationAmount =
                    Math.round(((amount * 5) / 100) * 100) / 100;
                  const effectiveProfit = amount - purificationAmount;
                  const clientProfit =
                    Math.round(((effectiveProfit * 40) / 68) * 100) / 100;
                  const mavenProfit =
                    Math.round(((effectiveProfit * 28) / 68) * 100) / 100;

                  // Calculate profit percentage
                  const totalCapital =
                    profitPeriodType === 'quarterly'
                      ? quarterlyCapitalData?.totalCapital || 0
                      : organization?.total_capital_monthly || 0;

                  const profitPercentage =
                    totalCapital > 0 ? (clientProfit / totalCapital) * 100 : 0;

                  return (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Maven (28/68)
                          </p>
                          <p className="font-bold text-blue-600">
                            ₹{mavenProfit.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Client (40/68)
                          </p>
                          <p className="font-bold text-green-600">
                            ₹{clientProfit.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Purification (5%)
                          </p>
                          <p className="font-bold text-orange-600">
                            ₹{purificationAmount.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Effective Profit
                          </p>
                          <p className="font-bold">
                            ₹{effectiveProfit.toLocaleString()}
                          </p>
                        </div>
                      </div>

                      {totalCapital > 0 && (
                        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
                          <p className="text-sm text-blue-800 dark:text-blue-200">
                            <strong>Profit Rate:</strong>{' '}
                            {profitPercentage.toFixed(4)}% on total{' '}
                            {profitPeriodType} capital of ₹
                            {totalCapital.toLocaleString()}
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })()}
                {parseFloat(profitAmount || '0') === 0 && (
                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      <strong>Note:</strong> 0% distribution will create initial
                      balance sheets for all eligible users without profit
                      distribution. This is useful for setting up the system for
                      the first time.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Enhanced Eligible Users for Distribution */}
          {profitPeriodType && (
            <Card className="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
              <CardHeader>
                <CardTitle className="text-blue-800 dark:text-blue-200">
                  Eligible Users for Distribution
                </CardTitle>
                <CardDescription>
                  {profitPeriodType === 'quarterly'
                    ? `Users with positions in ${
                        manualQuarter?.toUpperCase() || 'selected quarter'
                      }`
                    : `Users with ${profitPeriodType} investment status`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Name</th>
                        <th className="text-left p-2">Email</th>
                        <th className="text-left p-2">
                          {profitPeriodType === 'quarterly'
                            ? 'Quarter Position'
                            : 'Investment Status'}
                        </th>
                        <th className="text-right p-2">
                          {profitPeriodType === 'monthly'
                            ? 'Monthly'
                            : 'Quarterly'}{' '}
                          Capital
                        </th>
                        <th className="text-right p-2">Expected Profit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(() => {
                        let eligibleUsers = [];
                        let totalCapital = 0;

                        if (profitPeriodType === 'monthly') {
                          // Filter users based on monthly status
                          eligibleUsers = allClients.filter((client) =>
                            ['new_month_1', 'new_month_2'].includes(
                              client.investment_status
                            )
                          );
                          totalCapital =
                            organizationData?.data?.total_capital_monthly || 0;
                        } else if (
                          profitPeriodType === 'quarterly' &&
                          manualQuarter
                        ) {
                          // For quarterly periods, get users with positions in the selected quarter
                          if (
                            quarterlyCapitalData &&
                            quarterlyCapitalData.quarterlyPositions
                          ) {
                            // Use quarterly positions data for the selected quarter
                            eligibleUsers =
                              quarterlyCapitalData.quarterlyPositions.map(
                                (position: any) => ({
                                  ...position.client,
                                  quarterly_capital: position.closing_balance,
                                  quarter_position: position.quarter_name,
                                })
                              );
                            totalCapital = quarterlyCapitalData.totalCapital;
                          } else {
                            // No quarterly data available for this quarter
                            eligibleUsers = [];
                            totalCapital = 0;
                          }
                        }

                        if (eligibleUsers.length === 0) {
                          return (
                            <tr>
                              <td
                                colSpan={5}
                                className="p-4 text-center text-muted-foreground"
                              >
                                {profitPeriodType === 'quarterly' &&
                                !manualQuarter
                                  ? 'Please select a quarter to view eligible users'
                                  : profitPeriodType === 'quarterly' &&
                                    manualQuarter &&
                                    !quarterlyCapitalData
                                  ? `No positions found for quarter ${manualQuarter.toUpperCase()}. Users need to have active positions in this quarter.`
                                  : `No eligible users found for ${profitPeriodType} distribution`}
                              </td>
                            </tr>
                          );
                        }

                        return eligibleUsers.map((user: any) => {
                          const capital =
                            profitPeriodType === 'monthly'
                              ? user.monthly_capital
                              : user.quarterly_capital;

                          const amount = parseFloat(profitAmount || '0');
                          const purificationAmount =
                            Math.round(((amount * 5) / 100) * 100) / 100;
                          const effectiveProfit = amount - purificationAmount;
                          const clientProfitAmount =
                            Math.round(((effectiveProfit * 40) / 68) * 100) /
                            100;

                          // Calculate profit percentage and expected profit
                          const profitPercentage =
                            totalCapital > 0
                              ? clientProfitAmount / totalCapital
                              : 0;
                          const expectedProfit =
                            Math.round(capital * profitPercentage * 100) / 100;

                          return (
                            <tr key={user.id} className="border-b">
                              <td className="p-2">
                                {user.first_name} {user.last_name}
                              </td>
                              <td className="p-2">{user.email}</td>
                              <td className="p-2">
                                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                                  {profitPeriodType === 'quarterly'
                                    ? user.quarter_position?.toUpperCase()
                                    : user.investment_status}
                                </span>
                              </td>
                              <td className="p-2 text-right">
                                ₹{capital?.toLocaleString() || '0'}
                              </td>
                              <td className="p-2 text-right font-medium text-green-600">
                                ₹{expectedProfit.toLocaleString()}
                              </td>
                            </tr>
                          );
                        });
                      })()}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Phase 7: Quarter Transitions */}
      {/* <Card className="border-purple-200 bg-purple-50 dark:bg-purple-950 dark:border-purple-800">
        <CardHeader>
          <CardTitle className="text-purple-800 dark:text-purple-200">
            Quarter Transitions
          </CardTitle>
          <CardDescription className="text-purple-600 dark:text-purple-400">
            Handle quarter feeding logic for quarterly-only users
            (Q1→Q4→Q7→Q10→Q1, etc.)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Completed Quarter</Label>
              <Select
                value={selectedQuarterForTransition}
                onValueChange={setSelectedQuarterForTransition}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select completed quarter" />
                </SelectTrigger>
                <SelectContent>
                  {QUARTERS.map((quarter) => (
                    <SelectItem key={quarter.id} value={quarter.id}>
                      {quarter.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Target Quarter</Label>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="text-sm text-muted-foreground">
                  {selectedQuarterForTransition
                    ? (() => {
                        const quarterFeeding = {
                          Q1: 'Q4',
                          Q4: 'Q7',
                          Q7: 'Q10',
                          Q10: 'Q1',
                          Q2: 'Q5',
                          Q5: 'Q8',
                          Q8: 'Q11',
                          Q11: 'Q2',
                          Q3: 'Q6',
                          Q6: 'Q9',
                          Q9: 'Q12',
                          Q12: 'Q3',
                        };
                        return (
                          quarterFeeding[
                            selectedQuarterForTransition as keyof typeof quarterFeeding
                          ] || 'Unknown'
                        );
                      })()
                    : 'Select a quarter to see target'}
                </p>
              </div>
            </div>
          </div>

          <Button
            onClick={handleQuarterTransitions}
            disabled={
              !selectedQuarterForTransition ||
              handleQuarterTransitionsMutation.isPending
            }
            className="w-full bg-purple-600 hover:bg-purple-700"
          >
            {handleQuarterTransitionsMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing Quarter Transitions...
              </>
            ) : (
              'Execute Quarter Transitions'
            )}
          </Button>

          <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Note:</strong> This will transfer all quarterly-only
              users' closing balances from the completed quarter to the target
              quarter and create quarterly lineage records.
            </p>
          </div>
        </CardContent>
      </Card> */}

      {/* Phase 8: Cycle Completion */}
      {/* <Card className="border-indigo-200 bg-indigo-50 dark:bg-indigo-950 dark:border-indigo-800">
        <CardHeader>
          <CardTitle className="text-indigo-800 dark:text-indigo-200">
            Phase 8: Cycle Completion
          </CardTitle>
          <CardDescription className="text-indigo-600 dark:text-indigo-400">
            Finalize cycle completion and update organization/user completed
            cycles
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Admin Balance Sheet</Label>
            <Select
              value={selectedAdminBalanceSheetForCompletion}
              onValueChange={setSelectedAdminBalanceSheetForCompletion}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select admin balance sheet" />
              </SelectTrigger>
              <SelectContent>
                {adminBalanceSheets.map((sheet) => (
                  <SelectItem key={sheet.id} value={sheet.id}>
                    {sheet.period_type} -{' '}
                    {sheet.quarter_name || `Month ${sheet.period_month}`} (
                    {sheet.period_year}) - ₹
                    {sheet.total_profit_input?.toLocaleString() || 0}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={handleCompleteCycle}
            disabled={
              !selectedAdminBalanceSheetForCompletion ||
              completeCycleMutation.isPending
            }
            className="w-full bg-indigo-600 hover:bg-indigo-700"
          >
            {completeCycleMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Completing Cycle...
              </>
            ) : (
              'Complete Cycle'
            )}
          </Button>

          <div className="p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Note:</strong> This will increment organization and user
              completed_cycles, handle quarter transitions for quarterly
              distributions, and update quarter snapshots.
            </p>
          </div>
        </CardContent>
      </Card> */}

      {/* Open Windows Management */}
      {openWindows.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-orange-800">
              Open Transaction Windows ({openWindows.length}/2)
            </CardTitle>
            <CardDescription className="text-orange-600">
              Manage currently open transaction windows. You can have maximum 2
              windows open (1 monthly + 1 quarterly).
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {openWindows.map((window) => (
              <div
                key={window.id}
                className="flex items-center justify-between p-4 bg-white rounded-lg border"
              >
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium capitalize">
                      {window.period_type} Window
                    </span>
                    <span className="text-sm text-gray-500">
                      ({window.transaction_window_duration} days)
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span>
                      Start:{' '}
                      {new Date(window.window_start_date).toLocaleDateString()}
                    </span>
                    <span className="mx-2">•</span>
                    <span>
                      End:{' '}
                      {new Date(window.window_end_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Created: {new Date(window.created_at).toLocaleDateString()}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => handleCloseWindow(window.id)}
                    disabled={
                      closeWindowMutation.isPending ||
                      closeWindowWithUpdatesMutation.isPending
                    }
                    variant="outline"
                    size="sm"
                    className="text-orange-600 border-orange-200 hover:bg-orange-50"
                  >
                    {closeWindowMutation.isPending ? (
                      <>
                        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        Closing...
                      </>
                    ) : (
                      'Simple Close'
                    )}
                  </Button>
                  <Button
                    onClick={() => handleCloseWindowWithUpdates(window.id)}
                    disabled={
                      closeWindowMutation.isPending ||
                      closeWindowWithUpdatesMutation.isPending
                    }
                    variant="default"
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {closeWindowWithUpdatesMutation.isPending ? (
                      <>
                        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      'Close & Process'
                    )}
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Profit;
