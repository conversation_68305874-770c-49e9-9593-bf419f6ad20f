/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { useSafeArea } from '@/hooks/useSafeArea';

import {
  Home,
  Users,
  FileText,
  Sun,
  Moon,
  LogOut,
  Calculator,
  Database,
} from 'lucide-react';

import {
  useOrganizationClients,
  useOrganizationData,
  useOrganizationInvestmentRequests,
  useOrganizationWithdrawalRequests,
} from '@/hooks/useApi';
import {
  Overview,
  Clients,
  Requests,
  Profit,
  Balance,
} from '@/components/admin';

interface DistributionCalculation {
  mavenProfit: number;
  clientProfit: number;
  purification: number;
  total: number;
}

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const safeArea = useSafeArea();

  const [activeTab, setActiveTab] = useState('overview');

  // React Query hooks for data fetching
  const { data: organizationData, isLoading: orgLoading } = useOrganizationData(
    user?.organization_id || ''
  );

  const { data: clientsData, isLoading: clientsLoading } =
    useOrganizationClients(user?.organization_id || '');

  const { data: investmentRequestsData, isLoading: investmentRequestsLoading } =
    useOrganizationInvestmentRequests(user?.organization_id || '');

  const { data: withdrawalRequestsData, isLoading: withdrawalRequestsLoading } =
    useOrganizationWithdrawalRequests(user?.organization_id || '');

  // Derived data
  const clients = clientsData?.data || [];
  const pendingInvestmentRequests =
    investmentRequestsData?.data?.filter((r: any) => r.status === 'pending') ||
    [];
  const pendingWithdrawalRequests =
    withdrawalRequestsData?.data?.filter((r: any) => r.status === 'pending') ||
    [];
  const pendingRequests = [
    ...pendingInvestmentRequests,
    ...pendingWithdrawalRequests,
  ];
  const isLoading =
    orgLoading ||
    clientsLoading ||
    investmentRequestsLoading ||
    withdrawalRequestsLoading;

  // Dashboard data calculation
  const dashboardData = {
    total_capital: organizationData?.data?.total_investments || 0,
    total_clients: clients.length,
    pending_requests: pendingRequests.length,
    total_profit: 0,
  };

  const totalCapital = dashboardData.total_capital;

  const calculateProfitDistribution = (
    totalProfit: number
  ): DistributionCalculation => {
    // Step 1: Remove 5% as purification from entered amount
    const purificationPercentage = 5;
    const purification = (totalProfit * purificationPercentage) / 100;

    // Step 2: Remaining 95% is now treated as 68% for distribution
    const remainingAmount = totalProfit - purification;

    // Step 3: From this 68% (which is the remaining 95%):
    // Maven Share: 28% and Client Share: 40%
    const mavenProfit = (remainingAmount * 28) / 68;
    const clientProfit = (remainingAmount * 40) / 68;

    return {
      mavenProfit,
      clientProfit,
      purification,
      total: mavenProfit + clientProfit + purification,
    };
  };

  const renderActiveContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <Overview
            totalCapital={totalCapital}
            clients={clients}
            pendingRequests={pendingRequests}
            organizationData={organizationData?.data}
          />
        );
      case 'clients':
        return <Clients />;
      case 'requests':
        return <Requests />;
      case 'profit':
        return (
          <Profit calculateProfitDistribution={calculateProfitDistribution} />
        );
      case 'balance':
        return <Balance />;
      default:
        return (
          <Overview
            totalCapital={totalCapital}
            clients={clients}
            pendingRequests={pendingRequests}
            organizationData={organizationData?.data}
          />
        );
    }
  };

  return (
    <div
      className="min-h-screen bg-background pb-20"
      style={{ paddingTop: Math.max(safeArea.top, 16) }}
    >
      {/* Header */}
      <div className="bg-card border-b px-4 py-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold">Admin Dashboard</h1>
          <p className="text-sm text-muted-foreground">Welcome, {user?.name}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={toggleTheme}>
            {theme === 'dark' ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={logout}>
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>

      <div className="p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          renderActiveContent()
        )}
      </div>

      {/* Bottom Navigation */}
      <div
        className="fixed bottom-0 left-0 right-0 bg-card border-t px-4 py-2 flex justify-around items-center"
        style={{ paddingBottom: Math.max(safeArea.bottom, 16) }}
      >
        <Button
          variant={activeTab === 'overview' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('overview')}
          className="flex flex-col gap-1 h-auto py-2 px-2"
        >
          <Home className="h-4 w-4" />
          <span className="text-xs">Overview</span>
        </Button>
        <Button
          variant={activeTab === 'clients' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('clients')}
          className="flex flex-col gap-1 h-auto py-2 px-2"
        >
          <Users className="h-4 w-4" />
          <span className="text-xs">Clients</span>
        </Button>
        <Button
          variant={activeTab === 'requests' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('requests')}
          className="flex flex-col gap-1 h-auto py-2 px-2"
        >
          <FileText className="h-4 w-4" />
          <span className="text-xs">Requests</span>
        </Button>
        <Button
          variant={activeTab === 'profit' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('profit')}
          className="flex flex-col gap-1 h-auto py-2 px-2"
        >
          <Calculator className="h-4 w-4" />
          <span className="text-xs">Profit</span>
        </Button>
        <Button
          variant={activeTab === 'balance' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('balance')}
          className="flex flex-col gap-1 h-auto py-2 px-2"
        >
          <Database className="h-4 w-4" />
          <span className="text-xs">Balance</span>
        </Button>
      </div>
    </div>
  );
};

export default AdminDashboard;
