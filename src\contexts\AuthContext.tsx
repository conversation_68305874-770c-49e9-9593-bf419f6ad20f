/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { createContext, useContext, useEffect, useState } from 'react';
import { authApi } from '@/lib/api';

type UserRole = 'admin' | 'client';

export interface User {
  id: string;
  name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: UserRole;
  organization_id: string;
  investment_status:
    | 'new_month_1'
    | 'new_month_2'
    | 'new_month_3'
    | 'quarterly_only';
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const checkSession = async () => {
      try {
        const { data, error } = await authApi.getCurrentUserWithProfile();

        if (data && data.profile) {
          setUser({
            id: data.profile.id,
            name: `${data.profile.first_name} ${data.profile.last_name}`,
            first_name: data.profile.first_name,
            last_name: data.profile.last_name,
            email: data.profile.email,
            phone: data.profile.phone,
            role: data.profile.role,
            organization_id: data.profile.organization_id,
            investment_status: data.profile.investment_status,
          });
        }
      } catch (error) {
        console.error('Session check error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const { data, error } = await authApi.signIn(email, password);

      if (error || !data?.profile) {
        return false;
      }

      setUser({
        id: data.profile.id,
        name: `${data.profile.first_name} ${data.profile.last_name}`,
        first_name: data.profile.first_name,
        last_name: data.profile.last_name,
        email: data.profile.email,
        phone: data.profile.phone,
        role: data.profile.role,
        organization_id: data.profile.organization_id,
        investment_status: data.profile.investment_status,
      });

      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authApi.signOut();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
