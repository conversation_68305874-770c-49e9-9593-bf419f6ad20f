
import { useEffect, useState } from 'react';
import { SafeArea } from 'capacitor-plugin-safe-area';

interface SafeAreaInsets {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState<SafeAreaInsets>({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  });

  useEffect(() => {
    const getSafeArea = async () => {
      try {
        const safeAreaData = await SafeArea.getSafeAreaInsets();
        setSafeArea(safeAreaData.insets);
      } catch (error) {
        console.log('Safe area not available:', error);
      }
    };

    getSafeArea();
  }, []);

  return safeArea;
};
