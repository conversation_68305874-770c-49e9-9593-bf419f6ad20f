import { QueryClient } from '@tanstack/react-query';

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Query Keys Factory
export const queryKeys = {
  // User related queries
  user: {
    all: ['users'] as const,
    profile: (userId: string) =>
      [...queryKeys.user.all, 'profile', userId] as const,
    clients: (organizationId: string) =>
      [...queryKeys.user.all, 'clients', organizationId] as const,
    byInvestmentStatus: (organizationId: string, statuses: string[]) =>
      [
        ...queryKeys.user.all,
        'by-investment-status',
        organizationId,
        statuses.sort().join(','),
      ] as const,
  },

  // Investment requests
  investmentRequests: {
    all: ['investment-requests'] as const,
    byClient: (clientId: string) =>
      [...queryKeys.investmentRequests.all, 'client', clientId] as const,
    byOrganization: (organizationId: string) =>
      [
        ...queryKeys.investmentRequests.all,
        'organization',
        organizationId,
      ] as const,
    pending: (organizationId: string) =>
      [...queryKeys.investmentRequests.all, 'pending', organizationId] as const,
  },

  // Withdrawal requests
  withdrawalRequests: {
    all: ['withdrawal-requests'] as const,
    byClient: (clientId: string) =>
      [...queryKeys.withdrawalRequests.all, 'client', clientId] as const,
    byOrganization: (organizationId: string) =>
      [
        ...queryKeys.withdrawalRequests.all,
        'organization',
        organizationId,
      ] as const,
    pending: (organizationId: string) =>
      [...queryKeys.withdrawalRequests.all, 'pending', organizationId] as const,
  },

  // Balance sheets
  balanceSheets: {
    all: ['balance-sheets'] as const,
    byClient: (clientId: string) =>
      [...queryKeys.balanceSheets.all, 'client', clientId] as const,
    byOrganization: (organizationId: string) =>
      [...queryKeys.balanceSheets.all, 'organization', organizationId] as const,
    latest: (clientId: string) =>
      [...queryKeys.balanceSheets.all, 'latest', clientId] as const,
  },

  // Dashboard data
  dashboard: {
    all: ['dashboard'] as const,
    overview: (userId: string) =>
      [...queryKeys.dashboard.all, 'overview', userId] as const,
    adminOverview: (organizationId: string) =>
      [...queryKeys.dashboard.all, 'admin-overview', organizationId] as const,
    transactions: (userId: string) =>
      [...queryKeys.dashboard.all, 'transactions', userId] as const,
  },

  // Notifications
  notifications: {
    all: ['notifications'] as const,
    byUser: (userId: string) =>
      [...queryKeys.notifications.all, 'user', userId] as const,
    unreadCount: (userId: string) =>
      [...queryKeys.notifications.all, 'unread-count', userId] as const,
  },

  // Organizations
  organizations: {
    all: ['organizations'] as const,
    byId: (organizationId: string) =>
      [...queryKeys.organizations.all, organizationId] as const,
  },
} as const;

// Helper function to invalidate related queries
export const invalidateQueries = {
  // When user profile is updated
  userProfile: (userId: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.user.profile(userId) });
    queryClient.invalidateQueries({
      queryKey: queryKeys.dashboard.overview(userId),
    });
  },

  // When investment request is created/updated
  investmentRequests: (clientId: string, organizationId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.investmentRequests.byClient(clientId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.investmentRequests.byOrganization(organizationId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.investmentRequests.pending(organizationId),
    });
    // Also invalidate user profile as total_investment might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.profile(clientId),
    });
    // Invalidate organization data as capital amounts might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.organizations.byId(organizationId),
    });
    // Invalidate clients list as investment status might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.clients(organizationId),
    });
  },

  // When withdrawal request is created/updated
  withdrawalRequests: (clientId: string, organizationId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.withdrawalRequests.byClient(clientId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.withdrawalRequests.byOrganization(organizationId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.withdrawalRequests.pending(organizationId),
    });
    // Also invalidate user profile as total_investment might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.profile(clientId),
    });
    // Invalidate organization data as capital amounts might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.organizations.byId(organizationId),
    });
    // Invalidate clients list as investment status might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.clients(organizationId),
    });
  },

  // When balance sheet is updated
  balanceSheets: (clientId: string, organizationId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.balanceSheets.byClient(clientId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.balanceSheets.byOrganization(organizationId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.balanceSheets.latest(clientId),
    });
    // Also invalidate user profile as financial metrics might have changed
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.profile(clientId),
    });
  },

  // When notification is created/read
  notifications: (userId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.byUser(userId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.notifications.unreadCount(userId),
    });
  },
};
