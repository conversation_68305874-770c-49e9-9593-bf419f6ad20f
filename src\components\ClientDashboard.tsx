/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/button';
import { useSafeArea } from '@/hooks/useSafeArea';
import { useToast } from '@/hooks/use-toast';
import {
  Home,
  History,
  TrendingUp,
  User,
  Sun,
  Moon,
  LogOut,
} from 'lucide-react';
import { dashboardApi, requestApi, adminBalanceSheetApi } from '@/lib/supabase';
import { userApi } from '@/lib/api';

// Import the new components
import { Overview, History as HistoryView, Invest, Profile } from './client';

const ClientDashboard = () => {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const { toast } = useToast();
  const safeArea = useSafeArea();

  const [activeTab, setActiveTab] = useState('overview');
  const [currentPage, setCurrentPage] = useState(1);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [investAmount, setInvestAmount] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Data states
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [pendingRequests, setPendingRequests] = useState<any[]>([]);
  const [activeWindow, setActiveWindow] = useState<any>(null);

  const itemsPerPage = 10;

  // Current quarter (could be fetched from API or system settings)
  const currentQuarter = dashboardData?.current_quarter || 'q1';
  const loadDashboardData = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Load user profile with financial data
      const { data: profileData } = await userApi.getUserProfile(user.id);
      if (profileData) {
        setUserProfile(profileData);
      }

      // Load client overview
      const { data: overviewData } = await dashboardApi.getClientOverview(
        user.id
      );
      if (overviewData) {
        setDashboardData(overviewData);
      }

      // Load pending requests
      const { data: requestsData } = await requestApi.getUserRequests(user.id);
      if (requestsData) {
        const pendingOnly = requestsData.filter((r) => r.status === 'pending');
        setPendingRequests(pendingOnly);
      }

      // Load active transaction window for this user
      const { data: windowData } =
        await adminBalanceSheetApi.getActiveTransactionWindowForUser(
          user.organization_id,
          user.id
        );
      setActiveWindow(windowData); // Note: nested data structure
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, toast]);

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user, loadDashboardData]);

  const handleWithdrawRequest = async () => {
    // Check if transaction window is active
    if (!activeWindow) {
      toast({
        title: 'Transaction Window Closed',
        description:
          'Withdrawal requests can only be submitted during active transaction windows. Please wait for the next window to open.',
        variant: 'destructive',
      });
      return;
    }

    const amount = parseFloat(withdrawAmount);
    if (!amount || amount <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid withdrawal amount',
        variant: 'destructive',
      });
      return;
    }

    const availableForWithdrawal = userProfile?.withdrawal_funds || 0;
    if (amount > availableForWithdrawal) {
      toast({
        title: 'Insufficient Balance',
        description: `Withdrawal amount exceeds available funds. Available: ₹${availableForWithdrawal.toLocaleString()}`,
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await requestApi.createRequest({
        organization_id: user.organization_id,
        user_id: user.id,
        request_type: 'withdrawal',
        amount: amount,
        status: 'pending',
        quarter: currentQuarter,
        notes: `Withdrawal request for ₹${amount.toLocaleString()}`,
      });

      if (error) throw new Error(error.message);

      toast({
        title: 'Withdrawal Request Submitted',
        description: `Request for ₹${amount.toLocaleString()} submitted for admin approval`,
      });
      setWithdrawAmount('');

      // Reload data to show the new pending request
      loadDashboardData();
    } catch (error) {
      console.error('Error creating withdrawal request:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit withdrawal request',
        variant: 'destructive',
      });
    }
  };

  const handleInvestRequest = async () => {
    // Check if transaction window is active
    if (!activeWindow) {
      toast({
        title: 'Transaction Window Closed',
        description:
          'Investment requests can only be submitted during active transaction windows. Please wait for the next window to open.',
        variant: 'destructive',
      });
      return;
    }

    const amount = parseFloat(investAmount);
    if (!amount || amount <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid investment amount',
        variant: 'destructive',
      });
      return;
    }

    try {
      const { error } = await requestApi.createRequest({
        organization_id: user.organization_id,
        user_id: user.id,
        request_type: 'investment',
        amount: amount,
        status: 'pending',
        quarter: currentQuarter,
        notes: `Investment request for ₹${amount.toLocaleString()}`,
      });

      if (error) throw new Error(error.message);

      toast({
        title: 'Investment Request Submitted',
        description: `Request for ₹${amount.toLocaleString()} submitted for admin approval`,
      });
      setInvestAmount('');

      // Reload data to show the new pending request
      loadDashboardData();
    } catch (error) {
      console.error('Error creating investment request:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit investment request',
        variant: 'destructive',
      });
    }
  };

  const renderActiveContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <Overview
            userProfile={userProfile}
            pendingRequests={pendingRequests}
            withdrawAmount={withdrawAmount}
            setWithdrawAmount={setWithdrawAmount}
            investAmount={investAmount}
            setInvestAmount={setInvestAmount}
            handleWithdrawRequest={handleWithdrawRequest}
            handleInvestRequest={handleInvestRequest}
            currentQuarter={currentQuarter}
            activeWindow={activeWindow}
          />
        );
      case 'history':
        return (
          <HistoryView
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={itemsPerPage}
          />
        );
      case 'investments':
        return (
          <Invest availableForWithdrawal={userProfile?.withdrawal_funds || 0} />
        );
      case 'profile':
        return (
          <Profile
            user={user}
            userProfile={userProfile}
            currentQuarter={currentQuarter}
          />
        );
      default:
        return (
          <Overview
            userProfile={userProfile}
            pendingRequests={pendingRequests}
            withdrawAmount={withdrawAmount}
            setWithdrawAmount={setWithdrawAmount}
            investAmount={investAmount}
            setInvestAmount={setInvestAmount}
            handleWithdrawRequest={handleWithdrawRequest}
            handleInvestRequest={handleInvestRequest}
            currentQuarter={currentQuarter}
            activeWindow={activeWindow}
          />
        );
    }
  };

  return (
    <div
      className="min-h-screen bg-background pb-20"
      style={{ paddingTop: Math.max(safeArea.top, 16) }}
    >
      {/* Header */}
      <div className="bg-card border-b px-4 py-4 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold">Portfolio</h1>
          <p className="text-sm text-muted-foreground">Welcome, {user?.name}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={toggleTheme}>
            {theme === 'dark' ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={logout}>
            <LogOut className="h-4 w-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>

      <div className="p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          renderActiveContent()
        )}
      </div>

      {/* Bottom Navigation */}
      <div
        className="fixed bottom-0 left-0 right-0 bg-card border-t px-4 py-2 flex justify-around items-center"
        style={{ paddingBottom: Math.max(safeArea.bottom, 16) }}
      >
        <Button
          variant={activeTab === 'overview' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('overview')}
          className="flex flex-col gap-1 h-auto py-2"
        >
          <Home className="h-4 w-4" />
          <span className="text-xs">Overview</span>
        </Button>
        <Button
          variant={activeTab === 'history' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('history')}
          className="flex flex-col gap-1 h-auto py-2"
        >
          <History className="h-4 w-4" />
          <span className="text-xs">History</span>
        </Button>
        <Button
          variant={activeTab === 'investments' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('investments')}
          className="flex flex-col gap-1 h-auto py-2"
        >
          <TrendingUp className="h-4 w-4" />
          <span className="text-xs">Invest</span>
        </Button>
        <Button
          variant={activeTab === 'profile' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('profile')}
          className="flex flex-col gap-1 h-auto py-2"
        >
          <User className="h-4 w-4" />
          <span className="text-xs">Profile</span>
        </Button>
      </div>
    </div>
  );
};

export default ClientDashboard;
