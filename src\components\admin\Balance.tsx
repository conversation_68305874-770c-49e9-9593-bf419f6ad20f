/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useAuth } from '@/contexts/AuthContext';
import { useAdminBalanceSheets } from '@/hooks/useApi';
import { Loader2 } from 'lucide-react';
import { MONTHS, QUARTERS, formatCurrency } from '@/lib/constants';

interface BalanceRecord {
  id: string;
  period_type: string;
  period_month: number;
  period_year: number;
  quarter_name: string | null;
  total_profit_input: number;
  purification_rate: number;
  client_profit_rate: number;
  maven_profit_rate: number;
  purification_amount: number;
  client_profit_amount: number;
  maven_profit_amount: number;
  effective_profit: number;
  total_capital_base: number;
  profit_percentage: number;
  transaction_window_duration: number;
  window_start_date: string;
  window_end_date: string;
  is_window_closed: boolean;
  created_at: string;
}

interface BalanceProps {
  currentPage?: number;
  setCurrentPage?: (page: number) => void;
  itemsPerPage?: number;
}

const Balance: React.FC<BalanceProps> = ({
  currentPage = 1,
  setCurrentPage = () => {},
  itemsPerPage = 10,
}) => {
  const { user } = useAuth();
  const {
    data: adminBalanceSheetsData,
    isLoading,
    error,
  } = useAdminBalanceSheets(user?.organization_id || '');

  // Helper function to get month name
  const getMonthName = (monthNumber: number) => {
    const month = MONTHS.find((m) => m.number === monthNumber);
    return month ? month.label : `M${monthNumber}`;
  };

  const getPeriodLabel = (periodType: string, monthNumber: number) => {
    if (periodType === 'quarterly') {
      const quarterId = `Q${monthNumber}`;
      const quarter = QUARTERS.find((q) => q.id === quarterId);
      return quarter ? quarter.label : quarterId;
    }

    const month = MONTHS.find((m) => m.number === monthNumber);
    return month ? month.label : `M${monthNumber}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet (Admin)</h2>
        <Card>
          <CardContent className="p-6 flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading balance sheets...
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet (Admin)</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              Failed to load balance sheet data. Please try again later.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const balanceSheets = adminBalanceSheetsData?.data || [];

  if (balanceSheets.length === 0) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet (Admin)</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              No balance sheet records found. Balance sheets will appear here
              after profit distributions.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate pagination
  const totalPages = Math.ceil(balanceSheets.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedBalanceSheets = balanceSheets.slice(startIndex, endIndex);

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Balance Sheet (Admin)</h2>
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Profit Distribution History</CardTitle>
          <p className="text-sm text-muted-foreground">
            Detailed profit distribution records with transaction windows and
            capital base information
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Period</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Input Amount</TableHead>
                <TableHead>Capital Base</TableHead>
                <TableHead>Profit %</TableHead>
                <TableHead>Distribution</TableHead>
                <TableHead>Transaction Window</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBalanceSheets.map((record: BalanceRecord) => (
                <TableRow key={record.id}>
                  <TableCell>
                    <div>
                      <Badge variant="outline">
                        {getPeriodLabel(
                          record.period_type,
                          record.period_month
                        )}
                      </Badge>

                      <p className="text-xs text-muted-foreground mt-1">
                        {record.period_year}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        record.period_type === 'monthly'
                          ? 'default'
                          : 'secondary'
                      }
                      className={
                        record.period_type === 'monthly'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-green-100 text-green-800'
                      }
                    >
                      {record.period_type.toUpperCase()}
                    </Badge>
                    {record.quarter_name && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {record.quarter_name.toUpperCase()}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="font-semibold">
                    {formatCurrency(record.total_profit_input)}
                    <div className="text-xs text-muted-foreground">
                      (100% input)
                    </div>
                  </TableCell>
                  <TableCell className="font-semibold text-blue-600">
                    {formatCurrency(record.total_capital_base)}
                    <div className="text-xs text-muted-foreground">
                      {record.period_type} capital
                    </div>
                  </TableCell>
                  <TableCell className="font-semibold text-green-600">
                    {record.profit_percentage.toFixed(4)}%
                    <div className="text-xs text-muted-foreground">
                      client rate
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      <div className="text-green-600">
                        Client: {formatCurrency(record.client_profit_amount)} (
                        {record.client_profit_rate}%)
                      </div>
                      <div className="text-blue-600">
                        Maven: {formatCurrency(record.maven_profit_amount)} (
                        {record.maven_profit_rate}%)
                      </div>
                      <div className="text-orange-600">
                        Purification:{' '}
                        {formatCurrency(record.purification_amount)} (
                        {record.purification_rate}%)
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="font-medium">
                        {record.transaction_window_duration} days
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(
                          record.window_start_date
                        ).toLocaleDateString()}{' '}
                        -{' '}
                        {new Date(record.window_end_date).toLocaleDateString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        record.is_window_closed ? 'secondary' : 'default'
                      }
                      className={
                        record.is_window_closed
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-green-100 text-green-800'
                      }
                    >
                      {record.is_window_closed ? 'CLOSED' : 'ACTIVE'}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      className={
                        currentPage === 1
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  )}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      className={
                        currentPage === totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Balance;
