/* eslint-disable @typescript-eslint/no-explicit-any */
import { createClient } from '@supabase/supabase-js';
import { Database } from './database.types.js';

const supabaseUrl = 'https://jpqnvseejsahjlbhgmju.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwcW52c2VlanNhaGpsYmhnbWp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2NjY5NzUsImV4cCI6MjA2NjI0Mjk3NX0.-Vj_fwy4183U_helXzOcXFGua1EdOXqhH2qH1FDBTaw';
export const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Custom Auth APIs (using database-based authentication)
export const authApi = {
  signIn: async (email: string, password: string) => {
    try {
      // For demo purposes, use hardcoded credentials to bypass RLS issues
      // In production, you would apply the schema changes to disable RLS
      if (email === '<EMAIL>' && password === 'admin123') {
        const demoUser = {
          id: '550e8400-e29b-41d4-a716-446655440000',
          organization_id: '550e8400-e29b-41d4-a716-446655440001',
          email: '<EMAIL>',
          role: 'admin',
          full_name: 'Admin User',
          user_id: '<EMAIL>',
          is_active: true,
        };

        localStorage.setItem('user_session', JSON.stringify(demoUser));
        return { data: { user: demoUser }, error: null };
      } else if (
        email === '<EMAIL>' &&
        password === 'client123'
      ) {
        const demoUser = {
          id: '550e8400-e29b-41d4-a716-446655440002',
          organization_id: '550e8400-e29b-41d4-a716-446655440001',
          email: '<EMAIL>',
          role: 'client',
          full_name: 'Client User',
          user_id: '<EMAIL>',
          is_active: true,
        };

        localStorage.setItem('user_session', JSON.stringify(demoUser));
        return { data: { user: demoUser }, error: null };
      } else {
        return { data: null, error: { message: 'Invalid credentials' } };
      }
    } catch (error) {
      return { data: null, error };
    }
  },
  signOut: async () => {
    localStorage.removeItem('user_session');
    return { error: null };
  },
  getSession: async () => {
    try {
      const sessionData = localStorage.getItem('user_session');
      if (sessionData) {
        const user = JSON.parse(sessionData);
        return { data: { session: { user } }, error: null };
      }
      return { data: { session: null }, error: null };
    } catch (error) {
      return { data: { session: null }, error };
    }
  },
  getUser: async () => {
    try {
      const sessionData = localStorage.getItem('user_session');
      if (sessionData) {
        const user = JSON.parse(sessionData);
        return { data: { user }, error: null };
      }
      return { data: { user: null }, error: null };
    } catch (error) {
      return { data: { user: null }, error };
    }
  },
};

// User APIs
export const userApi = {
  getUsers: async (organizationId: string) => {
    return supabase
      .from('users')
      .select('*, client_accounts(*)')
      .eq('organization_id', organizationId);
  },
  getUserById: async (userId: string) => {
    return supabase
      .from('users')
      .select('*, client_accounts(*)')
      .eq('id', userId)
      .single();
  },
  createUser: async (userData: {
    email: string;
    full_name: string;
    user_id: string;
    password: string;
    role: string;
    organization_id: string;
  }) => {
    try {
      // Hash the password using the database function
      const { data: hashedPassword, error: hashError } = await supabase.rpc(
        'hash_password',
        {
          password: userData.password,
        }
      );

      if (hashError) {
        return { data: null, error: hashError };
      }

      // Create the user record in our users table
      const { data: userRecord, error: userError } = await supabase
        .from('users')
        .insert({
          organization_id: userData.organization_id,
          email: userData.email,
          password_hash: hashedPassword,
          role: userData.role as 'admin' | 'client',
          full_name: userData.full_name,
          user_id: userData.user_id,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      return { data: userRecord, error: userError };
    } catch (error) {
      return { data: null, error };
    }
  },
};

// Client Account APIs
export const clientAccountApi = {
  getClientAccount: async (userId: string) => {
    return supabase
      .from('client_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();
  },
  updateClientAccount: async (userId: string, updates: any) => {
    return supabase
      .from('client_accounts')
      .update(updates)
      .eq('user_id', userId);
  },
};

// Transaction APIs
export const transactionApi = {
  getTransactions: async (organizationId: string) => {
    return supabase
      .from('transactions')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },
  getUserTransactions: async (userId: string) => {
    return supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
  },
  createTransaction: async (transaction: {
    organization_id: string;
    user_id?: string;
    transaction_type: string;
    amount: number;
    quarter?: string;
    period_type?: string;
    description?: string;
    processed_by?: string;
  }) => {
    return supabase.from('transactions').insert({
      ...transaction,
      created_at: new Date().toISOString(),
    });
  },
};

// Request APIs
export const requestApi = {
  getRequests: async (organizationId: string, clientId?: string) => {
    let query = supabase
      .from('requests')
      .select('*, client:users(full_name, user_id), processor:users(full_name)')
      .eq('organization_id', organizationId);

    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    return query.order('created_at', { ascending: false });
  },
  getUserRequests: async (userId: string) => {
    return supabase
      .from('requests')
      .select('*')
      .eq('client_id', userId)
      .order('created_at', { ascending: false });
  },
  createRequest: async (request: {
    organization_id: string;
    user_id: string;
    request_type: 'investment' | 'withdrawal';
    amount: number;
    status: string;
    quarter?: string;
    notes?: string;
  }) => {
    return supabase.from('requests').insert({
      organization_id: request.organization_id,
      client_id: request.user_id,
      request_type: request.request_type,
      amount: request.amount,
      status: request.status,
      quarter: request.quarter,
      description: request.notes,
      created_at: new Date().toISOString(),
    });
  },
  updateRequestStatus: async (
    requestId: string,
    status: 'approved' | 'rejected',
    adminId: string,
    notes?: string
  ) => {
    return supabase
      .from('requests')
      .update({
        status,
        admin_notes: notes,
        processed_by: adminId,
        processed_at: new Date().toISOString(),
      })
      .eq('id', requestId);
  },
};

// Organization APIs
export const organizationApi = {
  getOrganization: async (organizationId: string) => {
    return supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();
  },
  updateOrganization: async (organizationId: string, updates: any) => {
    return supabase
      .from('organizations')
      .update(updates)
      .eq('id', organizationId);
  },
};

// PnL Entries APIs
export const pnlApi = {
  getPnLEntries: async (organizationId: string) => {
    return supabase
      .from('pnl_entries')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },
  createPnLEntry: async (pnlData: {
    organization_id: string;
    period: string;
    period_type: 'quarterly' | 'monthly';
    total_capital: number;
    pnl_amount: number;
    pnl_percentage: number;
    distributed_by?: string;
  }) => {
    return supabase.from('pnl_entries').insert({
      ...pnlData,
      created_at: new Date().toISOString(),
    });
  },
  updatePnLEntry: async (entryId: string, updates: any) => {
    return supabase.from('pnl_entries').update(updates).eq('id', entryId);
  },
};

// Profit Distribution APIs
export const profitDistributionApi = {
  getProfitDistributions: async (organizationId: string, clientId?: string) => {
    let query = supabase
      .from('profit_distributions')
      .select('*, pnl_entry:pnl_entries(*), client:users(full_name, user_id)')
      .eq('organization_id', organizationId);

    if (clientId) {
      query = query.eq('client_id', clientId);
    }

    return query.order('created_at', { ascending: false });
  },
  createProfitDistribution: async (distributionData: {
    organization_id: string;
    pnl_entry_id: string;
    client_id: string;
    client_capital: number;
    client_profit: number;
    maven_profit: number;
    purification_amount: number;
  }) => {
    return supabase.from('profit_distributions').insert({
      ...distributionData,
      created_at: new Date().toISOString(),
    });
  },
};

// Admin Balance Sheet APIs
export const adminBalanceSheetApi = {
  getAdminBalanceSheets: async (organizationId: string) => {
    return supabase
      .from('admin_balance_sheets')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },

  // Phase 2: Create initial window opening with 0% profit
  createInitialWindow: async (windowData: {
    organization_id: string;
    period_type: 'monthly' | 'quarterly';
    transaction_window_duration?: number;
  }) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    // Calculate window dates
    const windowDays = windowData.transaction_window_duration || 7;
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + windowDays);

    // For initial window, all profit values are 0
    const adminBalanceSheetData = {
      organization_id: windowData.organization_id,
      period_type: windowData.period_type,
      period_month: currentMonth,
      period_year: currentYear,
      quarter_name:
        windowData.period_type === 'quarterly'
          ? `q${Math.ceil(currentMonth / 3)}`
          : null,
      total_profit_input: 0,
      purification_rate: 5.0,
      client_profit_rate: 40.0,
      maven_profit_rate: 28.0,
      purification_amount: 0,
      client_profit_amount: 0,
      maven_profit_amount: 0,
      effective_profit: 0,
      total_capital_base: 0,
      profit_percentage: 0,
      transaction_window_duration: windowDays,
      window_start_date: startDate.toISOString(),
      window_end_date: endDate.toISOString(),
      is_window_closed: false,
    };

    return supabase
      .from('admin_balance_sheets')
      .insert(adminBalanceSheetData)
      .select()
      .single();
  },

  // Phase 2: Generate client balance sheets for eligible clients based on period type
  generateClientBalanceSheets: async (
    organizationId: string,
    adminBalanceSheetId: string,
    periodType: 'monthly' | 'quarterly'
  ) => {
    // Get eligible clients based on period type
    // Monthly: new_month_1 and new_month_2 users
    // Quarterly: quarterly_only users
    const eligibleStatuses =
      periodType === 'monthly'
        ? ['new_month_1', 'new_month_2']
        : ['quarterly_only'];

    const { data: clients, error: clientsError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('role', 'client')
      .eq('is_active', true)
      .in('investment_status', eligibleStatuses);

    if (clientsError) {
      throw new Error(`Failed to fetch clients: ${clientsError.message}`);
    }

    if (!clients || clients.length === 0) {
      return { data: [], error: null };
    }

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    // Create balance sheets for all clients
    const clientBalanceSheets = clients.map((client) => ({
      client_id: client.id,
      organization_id: organizationId,
      admin_balance_sheet_id: adminBalanceSheetId,
      period_type: periodType,
      period_month: currentMonth,
      period_year: currentYear,
      opening_balance: 0, // First time, all start with 0
      investments_made: 0,
      pnl_earned: 0,
      withdrawals_made: 0,
      closing_balance: 0,
      monthly_capital_used: 0,
      quarterly_capital_used: 0,
      profit_rate_applied: 0,
      user_status_at_creation: client.investment_status || 'new_month_1',
    }));

    return supabase
      .from('client_balance_sheets')
      .insert(clientBalanceSheets)
      .select();
  },

  // Phase 2: Complete initial window opening flow
  createInitialWindowWithClientSheets: async (windowData: {
    organization_id: string;
    period_type: 'monthly' | 'quarterly';
    transaction_window_duration?: number;
  }) => {
    try {
      // Check if we can create a window of this type
      const { canCreate, reason } = await adminBalanceSheetApi.canCreateWindow(
        windowData.organization_id,
        windowData.period_type
      );

      if (!canCreate) {
        throw new Error(reason || 'Cannot create window');
      }

      // Step 1: Create admin balance sheet with 0% profit
      const { data: adminBalanceSheet, error: adminError } =
        await adminBalanceSheetApi.createInitialWindow(windowData);

      if (adminError || !adminBalanceSheet) {
        throw new Error(
          `Failed to create admin balance sheet: ${adminError?.message}`
        );
      }

      // Step 2: Generate client balance sheets for all active clients
      const { data: clientBalanceSheets, error: clientError } =
        await adminBalanceSheetApi.generateClientBalanceSheets(
          windowData.organization_id,
          adminBalanceSheet.id,
          windowData.period_type
        );

      if (clientError) {
        throw new Error(
          `Failed to create client balance sheets: ${clientError.message}`
        );
      }

      return {
        data: {
          adminBalanceSheet,
          clientBalanceSheets: clientBalanceSheets || [],
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },
  createAdminBalanceSheet: async (balanceSheetData: {
    organization_id: string;
    period_type: string;
    period_month: number;
    period_year: number;
    quarter_name?: string | null;
    total_profit_input: number;
    purification_rate?: number;
    client_profit_rate?: number;
    maven_profit_rate?: number;
    purification_amount: number;
    client_profit_amount: number;
    maven_profit_amount: number;
    effective_profit: number;
    total_capital_base: number;
    profit_percentage: number;
    transaction_window_duration?: number;
    window_start_date: string;
    window_end_date: string;
  }) => {
    return supabase
      .from('admin_balance_sheets')
      .insert({
        ...balanceSheetData,
        purification_rate: balanceSheetData.purification_rate ?? 5.0,
        client_profit_rate: balanceSheetData.client_profit_rate ?? 40.0,
        maven_profit_rate: balanceSheetData.maven_profit_rate ?? 28.0,
        transaction_window_duration:
          balanceSheetData.transaction_window_duration ?? 7,
        is_window_closed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();
  },
  getBalanceSheetByPeriod: async (
    organizationId: string,
    periodMonth: number,
    periodYear: number
  ) => {
    return supabase
      .from('admin_balance_sheets')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('period_month', periodMonth)
      .eq('period_year', periodYear)
      .single();
  },
  updateAdminBalanceSheet: async (
    id: string,
    updateData: Partial<{
      total_profit_input: number;
      purification_amount: number;
      client_profit_amount: number;
      maven_profit_amount: number;
      effective_profit: number;
      profit_percentage: number;
      is_window_closed: boolean;
      window_end_date: string;
    }>
  ) => {
    return supabase
      .from('admin_balance_sheets')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);
  },
  getActiveTransactionWindow: async (organizationId: string) => {
    const today = new Date().toISOString();
    return (
      supabase
        .from('admin_balance_sheets')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('is_window_closed', false)
        // .lte('window_start_date', today)
        // .gte('window_end_date', today)
        .single()
    );
  },

  // Get active transaction window for a specific user based on their investment status
  getActiveTransactionWindowForUser: async (
    organizationId: string,
    userId: string
  ) => {
    // First get the user's investment status
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status')
      .eq('id', userId)
      .single();

    if (!userProfile) {
      return { data: null, error: new Error('User not found') };
    }

    const userStatus = userProfile.investment_status;

    // Determine which window types the user is eligible for
    let eligiblePeriodTypes: string[] = [];
    if (userStatus === 'new_month_1' || userStatus === 'new_month_2') {
      eligiblePeriodTypes = ['monthly'];
    } else if (userStatus === 'quarterly_only') {
      eligiblePeriodTypes = ['quarterly'];
    }

    if (eligiblePeriodTypes.length === 0) {
      return { data: null, error: null }; // User not eligible for any windows
    }

    // Get active windows for eligible period types
    const today = new Date().toISOString();
    return supabase
      .from('admin_balance_sheets')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_window_closed', false)
      .in('period_type', eligiblePeriodTypes)
      .limit(1)
      .single();
  },

  // Get all open windows (is_window_closed = false)
  getOpenWindows: async (organizationId: string) => {
    return supabase
      .from('admin_balance_sheets')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_window_closed', false)
      .order('created_at', { ascending: false });
  },

  // Check if we can create a new window of specific type
  canCreateWindow: async (
    organizationId: string,
    periodType: 'monthly' | 'quarterly'
  ) => {
    const { data: openWindows } = await adminBalanceSheetApi.getOpenWindows(
      organizationId
    );

    if (!openWindows) return { canCreate: true, reason: null };

    // Check if there's already an open window of this type
    const existingWindow = openWindows.find(
      (window) => window.period_type === periodType
    );

    if (existingWindow) {
      return {
        canCreate: false,
        reason: `A ${periodType} window is already open (created ${new Date(
          existingWindow.created_at
        ).toLocaleDateString()})`,
      };
    }

    return { canCreate: true, reason: null };
  },

  // Close a specific window
  closeWindow: async (windowId: string) => {
    return supabase
      .from('admin_balance_sheets')
      .update({
        is_window_closed: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', windowId);
  },

  closeWindowNormal: async (windowId: string) => {
    try {
      // Step 1: Get the admin balance sheet details
      const { data: adminBalanceSheet, error: adminError } = await supabase
        .from('admin_balance_sheets')
        .select('*')
        .eq('id', windowId)
        .single();

      if (adminError || !adminBalanceSheet) {
        throw new Error(
          `Failed to get admin balance sheet: ${adminError?.message}`
        );
      }

      // Step 2: Get all approved investments for this window
      const { data: approvedInvestments, error: investmentError } =
        await supabase
          .from('investment_requests')
          .select('*')
          .eq('admin_balance_sheet_id', windowId)
          .eq('status', 'approved');

      if (investmentError) {
        throw new Error(
          `Failed to get approved investments: ${investmentError.message}`
        );
      }

      // Step 3: Get all approved withdrawals for this window
      const { data: approvedWithdrawals, error: withdrawalError } =
        await supabase
          .from('withdrawal_requests')
          .select('*')
          .eq('admin_balance_sheet_id', windowId)
          .eq('status', 'approved');

      if (withdrawalError) {
        throw new Error(
          `Failed to get approved withdrawals: ${withdrawalError.message}`
        );
      }

      // Step 4: Close the admin balance sheet
      const { error: closeError } = await supabase
        .from('admin_balance_sheets')
        .update({
          is_window_closed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', windowId);

      if (closeError) {
        throw new Error(`Failed to close window: ${closeError.message}`);
      }

      // Step 5: Update client balance sheets
      const clientBalanceSheetUpdates =
        await adminBalanceSheetApi.updateClientBalanceSheets(
          windowId,
          approvedInvestments || [],
          approvedWithdrawals || []
        );

      if (clientBalanceSheetUpdates.error) {
        throw new Error(
          `Failed to update client balance sheets: ${clientBalanceSheetUpdates.error.message}`
        );
      }

      // Step 6: Update organization capitals
      const organizationUpdates =
        await adminBalanceSheetApi.updateOrganizationCapitals(
          adminBalanceSheet.organization_id,
          approvedInvestments || [],
          approvedWithdrawals || []
        );

      if (organizationUpdates.error) {
        throw new Error(
          `Failed to update organization capitals: ${organizationUpdates.error.message}`
        );
      }

      // NOTE: NO Step 7 - User status progression is skipped for normal close

      console.log(
        'Normal Close Results:',
        adminBalanceSheet,
        approvedInvestments,
        approvedWithdrawals,
        clientBalanceSheetUpdates,
        organizationUpdates
      );

      return {
        data: {
          adminBalanceSheet,
          approvedInvestments: approvedInvestments || [],
          approvedWithdrawals: approvedWithdrawals || [],
          clientBalanceSheetUpdates: clientBalanceSheetUpdates.data,
          organizationUpdates: organizationUpdates.data,
          statusUpdates: null, // No status updates for normal close
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 5: Complete window closing workflow
  closeWindowWithUpdates: async (windowId: string) => {
    try {
      // Step 1: Get the admin balance sheet details
      const { data: adminBalanceSheet, error: adminError } = await supabase
        .from('admin_balance_sheets')
        .select('*')
        .eq('id', windowId)
        .single();

      if (adminError || !adminBalanceSheet) {
        throw new Error(
          `Failed to get admin balance sheet: ${adminError?.message}`
        );
      }

      // Step 2: Get all approved investments for this window
      const { data: approvedInvestments, error: investmentError } =
        await supabase
          .from('investment_requests')
          .select('*')
          .eq('admin_balance_sheet_id', windowId)
          .eq('status', 'approved');

      if (investmentError) {
        throw new Error(
          `Failed to get approved investments: ${investmentError.message}`
        );
      }
      // Step 3: Get all approved withdrawals for this window
      const { data: approvedWithdrawals, error: withdrawalError } =
        await supabase
          .from('withdrawal_requests')
          .select('*')
          .eq('admin_balance_sheet_id', windowId)
          .eq('status', 'approved');

      if (withdrawalError) {
        throw new Error(
          `Failed to get approved withdrawals: ${withdrawalError.message}`
        );
      }

      // Step 4: Close the admin balance sheet
      const { error: closeError } = await supabase
        .from('admin_balance_sheets')
        .update({
          is_window_closed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', windowId);

      if (closeError) {
        throw new Error(`Failed to close window: ${closeError.message}`);
      }

      // Step 5: Update client balance sheets
      const clientBalanceSheetUpdates =
        await adminBalanceSheetApi.updateClientBalanceSheets(
          windowId,
          approvedInvestments || [],
          approvedWithdrawals || []
        );

      if (clientBalanceSheetUpdates.error) {
        throw new Error(
          `Failed to update client balance sheets: ${clientBalanceSheetUpdates.error.message}`
        );
      }

      // Step 6: Update organization capitals
      const organizationUpdates =
        await adminBalanceSheetApi.updateOrganizationCapitals(
          adminBalanceSheet.organization_id,
          approvedInvestments || [],
          approvedWithdrawals || []
        );

      if (organizationUpdates.error) {
        throw new Error(
          `Failed to update organization capitals: ${organizationUpdates.error.message}`
        );
      }

      // Step 7: Progress user statuses
      const statusUpdates = await adminBalanceSheetApi.progressUserStatuses(
        adminBalanceSheet.organization_id,
        [...(approvedInvestments || []), ...(approvedWithdrawals || [])]
      );

      if (statusUpdates.error) {
        throw new Error(
          `Failed to progress user statuses: ${statusUpdates.error.message}`
        );
      }

      console.log(
        adminBalanceSheet,
        approvedInvestments,
        approvedWithdrawals,
        clientBalanceSheetUpdates,
        organizationUpdates,
        statusUpdates
      );
      return {
        data: {
          adminBalanceSheet,
          approvedInvestments: approvedInvestments || [],
          approvedWithdrawals: approvedWithdrawals || [],
          clientBalanceSheetUpdates: clientBalanceSheetUpdates.data,
          organizationUpdates: organizationUpdates.data,
          statusUpdates: statusUpdates.data,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 5: Update client balance sheets with investment/withdrawal totals
  updateClientBalanceSheets: async (
    adminBalanceSheetId: string,
    approvedInvestments: any[],
    approvedWithdrawals: any[]
  ) => {
    try {
      // Group investments and withdrawals by client
      const clientUpdates = new Map();

      // Process investments
      approvedInvestments.forEach((investment) => {
        const clientId = investment.client_id;
        if (!clientUpdates.has(clientId)) {
          clientUpdates.set(clientId, { investments: 0, withdrawals: 0 });
        }
        clientUpdates.get(clientId).investments += investment.amount;
      });

      // Process withdrawals
      approvedWithdrawals.forEach((withdrawal) => {
        const clientId = withdrawal.client_id;
        if (!clientUpdates.has(clientId)) {
          clientUpdates.set(clientId, { investments: 0, withdrawals: 0 });
        }
        clientUpdates.get(clientId).withdrawals += withdrawal.amount;
      });

      // Update client balance sheets
      const updatePromises = Array.from(clientUpdates.entries()).map(
        async ([clientId, totals]) => {
          // Get the client's balance sheet for this admin balance sheet
          const { data: clientBalanceSheet } = await supabase
            .from('client_balance_sheets')
            .select('*')
            .eq('admin_balance_sheet_id', adminBalanceSheetId)
            .eq('client_id', clientId)
            .single();

          if (clientBalanceSheet) {
            const closingBalance =
              clientBalanceSheet.opening_balance +
              totals.investments -
              totals.withdrawals;

            return supabase
              .from('client_balance_sheets')
              .update({
                investments_made: totals.investments,
                withdrawals_made: totals.withdrawals,
                closing_balance: closingBalance,
                updated_at: new Date().toISOString(),
              })
              .eq('id', clientBalanceSheet.id);
          }
          return null;
        }
      );

      const results = await Promise.all(updatePromises);
      const errors = results.filter((result) => result?.error);

      if (errors.length > 0) {
        throw new Error(
          `Failed to update some client balance sheets: ${errors[0].error.message}`
        );
      }

      return {
        data: { updatedClients: clientUpdates.size },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 5: Update organization capitals based on approved investments/withdrawals
  updateOrganizationCapitals: async (
    organizationId: string,
    approvedInvestments: any[],
    approvedWithdrawals: any[]
  ) => {
    try {
      // Group capital changes by client
      const clientCapitalChanges = new Map();

      // Process investment capital additions
      approvedInvestments.forEach((investment) => {
        const clientId = investment.client_id;
        if (!clientCapitalChanges.has(clientId)) {
          clientCapitalChanges.set(clientId, {
            monthlyChange: 0,
            quarterlyChange: 0,
          });
        }
        const changes = clientCapitalChanges.get(clientId);
        changes.monthlyChange += investment.monthly_capital_added || 0;
        changes.quarterlyChange += investment.quarterly_capital_added || 0;
      });

      // Process withdrawal capital deductions
      approvedWithdrawals.forEach((withdrawal) => {
        const clientId = withdrawal.client_id;
        if (!clientCapitalChanges.has(clientId)) {
          clientCapitalChanges.set(clientId, {
            monthlyChange: 0,
            quarterlyChange: 0,
          });
        }
        const changes = clientCapitalChanges.get(clientId);
        changes.monthlyChange -= withdrawal.monthly_capital_deducted || 0;
        changes.quarterlyChange -= withdrawal.quarterly_capital_deducted || 0;
      });

      // Update user profile capitals (triggers will automatically update organization totals)
      const updatePromises = Array.from(clientCapitalChanges.entries()).map(
        async ([clientId, changes]) => {
          // Get current user capitals
          const { data: userProfile } = await supabase
            .from('user_profiles')
            .select('monthly_capital, quarterly_capital')
            .eq('id', clientId)
            .single();

          if (userProfile) {
            const newMonthlyCapital = Math.max(
              0,
              (userProfile.monthly_capital || 0) + changes.monthlyChange
            );
            const newQuarterlyCapital = Math.max(
              0,
              (userProfile.quarterly_capital || 0) + changes.quarterlyChange
            );

            return supabase
              .from('user_profiles')
              .update({
                monthly_capital: newMonthlyCapital,
                quarterly_capital: newQuarterlyCapital,
                updated_at: new Date().toISOString(),
              })
              .eq('id', clientId);
          }
          return null;
        }
      );

      const results = await Promise.all(updatePromises);
      const errors = results.filter((result) => result?.error);

      if (errors.length > 0) {
        throw new Error(
          `Failed to update some user capitals: ${errors[0].error.message}`
        );
      }

      // Calculate total changes for reporting
      let totalMonthlyChange = 0;
      let totalQuarterlyChange = 0;
      clientCapitalChanges.forEach((changes) => {
        totalMonthlyChange += changes.monthlyChange;
        totalQuarterlyChange += changes.quarterlyChange;
      });

      // Get updated organization totals (should be updated by triggers)
      const { data: organization } = await supabase
        .from('organizations')
        .select('total_capital_monthly, total_capital_quarterly')
        .eq('id', organizationId)
        .single();

      return {
        data: {
          monthlyCapitalChange: totalMonthlyChange,
          quarterlyCapitalChange: totalQuarterlyChange,
          newMonthlyCapital: organization?.total_capital_monthly || 0,
          newQuarterlyCapital: organization?.total_capital_quarterly || 0,
          updatedClients: clientCapitalChanges.size,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 5: Progress user statuses and increment completed cycles
  progressUserStatuses: async (organizationId: string, allRequests: any[]) => {
    try {
      // Get unique client IDs from all requests
      const clientIds = [
        ...new Set(allRequests.map((request) => request.client_id)),
      ];

      if (clientIds.length === 0) {
        return {
          data: { updatedUsers: 0 },
          error: null,
        };
      }

      // Get current user profiles
      const { data: userProfiles } = await supabase
        .from('user_profiles')
        .select('id, investment_status, completed_cycles')
        .eq('organization_id', organizationId)
        .in('id', clientIds);

      if (!userProfiles) {
        throw new Error('Failed to get user profiles');
      }

      // Progress user statuses
      const statusUpdates = userProfiles.map((user) => {
        let newStatus = user.investment_status;
        const newCompletedCycles = user.completed_cycles + 1;

        // Progress status based on current status
        switch (user.investment_status) {
          case 'new_month_1':
            newStatus = 'new_month_2';
            break;
          case 'new_month_2':
            newStatus = 'quarterly_only';
            break;
          case 'quarterly_only':
            // No status change for quarterly_only users
            newStatus = 'quarterly_only';
            break;
          default:
            newStatus = user.investment_status;
        }

        return {
          id: user.id,
          investment_status: newStatus,
          completed_cycles: newCompletedCycles,
          updated_at: new Date().toISOString(),
        };
      });

      // Update user profiles in batch
      const updatePromises = statusUpdates.map((update) =>
        supabase
          .from('user_profiles')
          .update({
            investment_status: update.investment_status,
            completed_cycles: update.completed_cycles,
            updated_at: update.updated_at,
          })
          .eq('id', update.id)
      );

      const results = await Promise.all(updatePromises);
      const errors = results.filter((result) => result.error);

      if (errors.length > 0) {
        throw new Error(
          `Failed to update some user statuses: ${errors[0].error.message}`
        );
      }

      return {
        data: {
          updatedUsers: statusUpdates.length,
          statusChanges: statusUpdates.map((update) => ({
            userId: update.id,
            newStatus: update.investment_status,
            newCompletedCycles: update.completed_cycles,
          })),
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },
  closeTransactionWindow: async (adminBalanceSheetId: string) => {
    return supabase
      .from('admin_balance_sheets')
      .update({
        is_window_closed: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', adminBalanceSheetId);
  },

  // Process window closure with investment/withdrawal updates and status progression
  processWindowClosure: async (adminBalanceSheetId: string) => {
    // Get the admin balance sheet
    const { data: adminSheet, error: adminError } = await supabase
      .from('admin_balance_sheets')
      .select('*')
      .eq('id', adminBalanceSheetId)
      .single();

    if (adminError || !adminSheet) {
      throw new Error('Admin balance sheet not found');
    }

    // Get all client balance sheets for this admin sheet
    const { data: clientSheets, error: clientError } = await supabase
      .from('client_balance_sheets')
      .select(
        `
        *,
        client:user_profiles!client_id(*)
      `
      )
      .eq('admin_balance_sheet_id', adminBalanceSheetId);

    if (clientError) {
      throw new Error('Failed to fetch client balance sheets');
    }

    // Get all approved investment requests for this window
    const { data: investmentRequests, error: investmentError } = await supabase
      .from('investment_requests')
      .select('*')
      .eq('admin_balance_sheet_id', adminBalanceSheetId)
      .eq('status', 'approved');

    if (investmentError) {
      throw new Error('Failed to fetch investment requests');
    }

    // Get all approved withdrawal requests for this window
    const { data: withdrawalRequests, error: withdrawalError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('admin_balance_sheet_id', adminBalanceSheetId)
      .eq('status', 'approved');

    if (withdrawalError) {
      throw new Error('Failed to fetch withdrawal requests');
    }

    // Group requests by client
    const clientInvestments = new Map();
    const clientWithdrawals = new Map();

    investmentRequests?.forEach((req) => {
      const current = clientInvestments.get(req.client_id) || 0;
      clientInvestments.set(req.client_id, current + req.amount);
    });

    withdrawalRequests?.forEach((req) => {
      const current = clientWithdrawals.get(req.client_id) || 0;
      clientWithdrawals.set(req.client_id, current + req.amount);
    });

    // Update each client balance sheet with actual investments/withdrawals
    for (const sheet of clientSheets || []) {
      const investmentAmount = clientInvestments.get(sheet.client_id) || 0;
      const withdrawalAmount = clientWithdrawals.get(sheet.client_id) || 0;

      // Calculate new closing balance
      const newClosingBalance =
        sheet.opening_balance +
        sheet.pnl_earned +
        investmentAmount -
        withdrawalAmount;

      // Update the client balance sheet
      await supabase
        .from('client_balance_sheets')
        .update({
          investments_made: investmentAmount,
          withdrawals_made: withdrawalAmount,
          closing_balance: newClosingBalance,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sheet.id);

      // Update user's withdrawal_funds to the new closing balance
      await supabase
        .from('user_profiles')
        .update({
          withdrawal_funds: newClosingBalance,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sheet.client_id);
    }

    // Progress user investment status based on completed cycles
    for (const sheet of clientSheets || []) {
      const client = sheet.client;
      if (!client) continue;

      // Only progress status if user has completed cycles
      if (client.completed_cycles > 0) {
        let newStatus = client.investment_status;
        let shouldUpdateCapital = false;
        let newMonthlyCapital = client.monthly_capital || 0;
        let newQuarterlyCapital = client.quarterly_capital || 0;

        if (client.investment_status === 'new_month_1') {
          // Progress to new_month_2
          newStatus = 'new_month_2';
          // Split closing balance 50/50 for next cycle
          const closingBalance = sheet.closing_balance;
          newMonthlyCapital = Math.round(closingBalance / 2);
          newQuarterlyCapital = closingBalance - newMonthlyCapital;
          shouldUpdateCapital = true;
        } else if (client.investment_status === 'new_month_2') {
          // Progress to quarterly_only
          newStatus = 'quarterly_only';
          // Move all to quarterly capital
          newQuarterlyCapital = sheet.closing_balance;
          newMonthlyCapital = 0;
          shouldUpdateCapital = true;
        }
        // quarterly_only users stay quarterly_only but update their quarterly capital
        else if (client.investment_status === 'quarterly_only') {
          newQuarterlyCapital = sheet.closing_balance;
          shouldUpdateCapital = true;
        }

        // Update user profile with new status and capital allocation
        const updateData: any = {
          investment_status: newStatus,
          completed_cycles: client.completed_cycles + 1,
          updated_at: new Date().toISOString(),
        };

        if (shouldUpdateCapital) {
          updateData.monthly_capital = newMonthlyCapital;
          updateData.quarterly_capital = newQuarterlyCapital;
        }

        await supabase
          .from('user_profiles')
          .update(updateData)
          .eq('id', sheet.client_id);
      }
    }

    // Close the transaction window
    await supabase
      .from('admin_balance_sheets')
      .update({
        is_window_closed: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', adminBalanceSheetId);

    return {
      success: true,
      clientsProcessed: clientSheets?.length || 0,
      totalInvestments: Array.from(clientInvestments.values()).reduce(
        (sum, val) => sum + val,
        0
      ),
      totalWithdrawals: Array.from(clientWithdrawals.values()).reduce(
        (sum, val) => sum + val,
        0
      ),
    };
  },

  // Phase 6: Get capital snapshot for profit percentage calculation
  getCapitalSnapshot: async (
    organizationId: string,
    periodType: 'monthly' | 'quarterly',
    quarterName?: string
  ) => {
    try {
      if (periodType === 'monthly') {
        // For monthly: Use current org total_capital_monthly
        const { data: organization } = await supabase
          .from('organizations')
          .select('total_capital_monthly, total_capital_quarterly')
          .eq('id', organizationId)
          .single();

        if (!organization) {
          throw new Error('Organization not found');
        }

        // Get total users count
        const { count: totalUsers } = await supabase
          .from('user_profiles')
          .select('*', { count: 'exact', head: true })
          .eq('organization_id', organizationId)
          .eq('role', 'client')
          .eq('is_active', true);
        console.log(totalUsers);
        return {
          data: {
            totalCapital: organization.total_capital_monthly,
            monthlyCapital: organization.total_capital_monthly,
            quarterlyCapital: 0,
            totalUsers: totalUsers || 0,
          },
          error: null,
        };
      } else {
        // For quarterly: Get capital from quarter_snapshots for that specific quarter
        if (!quarterName) {
          throw new Error(
            'Quarter name is required for quarterly profit distribution'
          );
        }

        const { data: quarterSnapshot } = await supabase
          .from('quarter_snapshots')
          .select('total_capital_at_end')
          .eq('organization_id', organizationId)
          .eq('quarter_name', quarterName)
          .eq('is_active', true)
          .single();

        if (!quarterSnapshot) {
          throw new Error(`Quarter snapshot not found for ${quarterName}`);
        }

        // Get total users count
        const { count: totalUsers } = await supabase
          .from('user_profiles')
          .select('*', { count: 'exact', head: true })
          .eq('organization_id', organizationId)
          .eq('role', 'client')
          .eq('is_active', true);

        return {
          data: {
            totalCapital: quarterSnapshot.total_capital_at_end,
            monthlyCapital: 0,
            quarterlyCapital: quarterSnapshot.total_capital_at_end,
            totalUsers: totalUsers || 0,
          },
          error: null,
        };
      }
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 6: Create profit distribution with capital snapshots
  createProfitDistribution: async (profitData: {
    organization_id: string;
    period_type: 'monthly' | 'quarterly';
    total_profit_input: number;
    quarter_name?: string;
    transaction_window_duration?: number;
  }) => {
    try {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();

      // Calculate profit breakdown (effective profit after purification, distributed as 40/68 and 28/68)
      const purificationRate = 5.0; // Remove 5% purification from input
      const effectiveProfitRate = 68.0; // Total distribution rate (40% + 28% = 68%)
      const clientProfitRate = 40.0; // Client gets 40 out of 68 parts
      const mavenProfitRate = 28.0; // Maven gets 28 out of 68 parts

      const purificationAmount =
        Math.round(
          ((profitData.total_profit_input * purificationRate) / 100) * 100
        ) / 100;
      const effectiveProfit =
        profitData.total_profit_input - purificationAmount;
      const clientProfitAmount =
        Math.round(
          ((effectiveProfit * clientProfitRate) / effectiveProfitRate) * 100
        ) / 100;
      const mavenProfitAmount =
        Math.round(
          ((effectiveProfit * mavenProfitRate) / effectiveProfitRate) * 100
        ) / 100;

      // Get capital snapshot for profit percentage calculation
      const capitalSnapshot = await adminBalanceSheetApi.getCapitalSnapshot(
        profitData.organization_id,
        profitData.period_type,
        profitData.quarter_name
      );
      console.log(capitalSnapshot);

      if (capitalSnapshot.error) {
        throw new Error(
          `Failed to get capital snapshot: ${capitalSnapshot.error.message}`
        );
      }

      const totalCapitalBase = capitalSnapshot.data?.totalCapital || 0;
      const profitPercentage =
        totalCapitalBase > 0
          ? Math.round((clientProfitAmount / totalCapitalBase) * 10000) / 10000
          : 0;

      // Calculate window dates
      const windowDuration = profitData.transaction_window_duration || 7;
      const windowStartDate = new Date();
      const windowEndDate = new Date();
      windowEndDate.setDate(windowEndDate.getDate() + windowDuration);

      // Create admin balance sheet
      const { data: adminBalanceSheet, error: createError } = await supabase
        .from('admin_balance_sheets')
        .insert({
          organization_id: profitData.organization_id,
          period_type: profitData.period_type,
          period_month: currentMonth,
          period_year: currentYear,
          quarter_name: profitData.quarter_name,
          total_profit_input: profitData.total_profit_input,
          purification_rate: purificationRate,
          client_profit_rate: clientProfitRate,
          maven_profit_rate: mavenProfitRate,
          purification_amount: purificationAmount,
          client_profit_amount: clientProfitAmount,
          maven_profit_amount: mavenProfitAmount,
          effective_profit: effectiveProfit,
          total_capital_base: totalCapitalBase,
          profit_percentage: profitPercentage,
          transaction_window_duration: windowDuration,
          window_start_date: windowStartDate.toISOString(),
          window_end_date: windowEndDate.toISOString(),
          is_window_closed: false,
        })
        .select()
        .single();

      if (createError) {
        throw new Error(
          `Failed to create admin balance sheet: ${createError.message}`
        );
      }

      const { data: organization } = await supabase
        .from('organizations')
        .select('total_maven_profit, total_client_profit, total_purification')
        .eq('id', profitData.organization_id)
        .single();

      if (!organization) {
        throw new Error('Organization not found');
      }

      // After creating admin balance sheet, update organization profits
      const { error: orgUpdateError } = await supabase
        .from('organizations')
        .update({
          total_maven_profit:
            (organization.total_maven_profit || 0) + mavenProfitAmount,
          total_client_profit:
            (organization.total_client_profit || 0) + clientProfitAmount,
          total_purification:
            (organization.total_purification || 0) + purificationAmount,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profitData.organization_id);

      if (orgUpdateError) {
        throw new Error(
          `Failed to update organization profits: ${orgUpdateError.message}`
        );
      }

      // Generate client balance sheets with PnL
      const clientBalanceSheets =
        await adminBalanceSheetApi.generateClientBalanceSheetsWithPnL(
          adminBalanceSheet.id,
          profitData.organization_id,
          profitPercentage
        );

      if (clientBalanceSheets.error) {
        throw new Error(
          `Failed to generate client balance sheets: ${clientBalanceSheets.error.message}`
        );
      }

      return {
        data: {
          adminBalanceSheet,
          capitalSnapshot: capitalSnapshot.data,
          clientBalanceSheets: clientBalanceSheets.data,
          profitCalculations: {
            purificationAmount,
            clientProfitAmount,
            mavenProfitAmount,
            effectiveProfit,
            profitPercentage,
          },
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 6: Generate client balance sheets with PnL for profit distribution
  generateClientBalanceSheetsWithPnL: async (
    adminBalanceSheetId: string,
    organizationId: string,
    profitPercentage: number
  ) => {
    try {
      // Get all eligible clients
      const { data: clients } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('role', 'client')
        .eq('is_active', true);

      if (!clients || clients.length === 0) {
        return {
          data: { clientBalanceSheets: [], updatedClients: 0 },
          error: null,
        };
      }

      // Get admin balance sheet details
      const { data: adminBalanceSheet } = await supabase
        .from('admin_balance_sheets')
        .select('*')
        .eq('id', adminBalanceSheetId)
        .single();

      if (!adminBalanceSheet) {
        throw new Error('Admin balance sheet not found');
      }

      // Generate client balance sheets
      const clientBalanceSheetPromises = clients.map(async (client) => {
        // Calculate opening balance using calculate_opening_balance() function
        const openingBalance =
          await adminBalanceSheetApi.calculateOpeningBalance(
            client.id,
            organizationId,
            adminBalanceSheet.period_type
          );

        // Calculate PnL based on their capital and profit percentage
        const capitalUsed =
          adminBalanceSheet.period_type === 'monthly'
            ? client.monthly_capital
            : client.quarterly_capital;

        const pnlEarned =
          Math.round(capitalUsed * profitPercentage * 100) / 100;
        const closingBalance = openingBalance + pnlEarned;

        // Insert client balance sheet
        const { data: clientBalanceSheet, error: insertError } = await supabase
          .from('client_balance_sheets')
          .insert({
            client_id: client.id,
            organization_id: organizationId,
            admin_balance_sheet_id: adminBalanceSheetId,
            period_type: adminBalanceSheet.period_type,
            period_month: adminBalanceSheet.period_month,
            period_year: adminBalanceSheet.period_year,
            opening_balance: openingBalance,
            investments_made: 0, // Will be updated during window closure
            pnl_earned: pnlEarned,
            withdrawals_made: 0, // Will be updated during window closure
            closing_balance: closingBalance,
            monthly_capital_used:
              adminBalanceSheet.period_type === 'monthly'
                ? client.monthly_capital
                : 0,
            quarterly_capital_used:
              adminBalanceSheet.period_type === 'quarterly'
                ? client.quarterly_capital
                : 0,
            profit_rate_applied: profitPercentage,
            user_status_at_creation: client.investment_status,
          })
          .select()
          .single();

        if (insertError) {
          throw new Error(
            `Failed to create client balance sheet for ${client.id}: ${insertError.message}`
          );
        }

        // Update user_profiles withdrawal_funds = new closing_balance
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            withdrawal_funds: closingBalance,
            total_profit: (client.total_profit || 0) + pnlEarned,
            updated_at: new Date().toISOString(),
          })
          .eq('id', client.id);

        if (updateError) {
          throw new Error(
            `Failed to update withdrawal funds for ${client.id}: ${updateError.message}`
          );
        }

        return clientBalanceSheet;
      });

      const clientBalanceSheets = await Promise.all(clientBalanceSheetPromises);

      // Create capital snapshot record
      const { error: snapshotError } = await supabase
        .from('capital_snapshots')
        .insert({
          admin_balance_sheet_id: adminBalanceSheetId,
          organization_id: organizationId,
          period_type: adminBalanceSheet.period_type,
          period_month: adminBalanceSheet.period_month,
          period_year: adminBalanceSheet.period_year,
          monthly_capital_snapshot:
            adminBalanceSheet.period_type === 'monthly'
              ? adminBalanceSheet.total_capital_base
              : 0,
          quarterly_capital_snapshot:
            adminBalanceSheet.period_type === 'quarterly'
              ? adminBalanceSheet.total_capital_base
              : 0,
          total_users_snapshot: clients.length,
        });

      if (snapshotError) {
        throw new Error(
          `Failed to create capital snapshot: ${snapshotError.message}`
        );
      }

      return {
        data: {
          clientBalanceSheets,
          updatedClients: clients.length,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 6: Calculate opening balance for client
  calculateOpeningBalance: async (
    clientId: string,
    organizationId: string,
    periodType: 'monthly' | 'quarterly'
  ): Promise<number> => {
    try {
      // Get the most recent client balance sheet for this client
      const { data: lastBalanceSheet } = await supabase
        .from('client_balance_sheets')
        .select('closing_balance')
        .eq('client_id', clientId)
        .eq('organization_id', organizationId)
        .eq('period_type', periodType)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      // If no previous balance sheet exists, opening balance is 0
      return lastBalanceSheet?.closing_balance || 0;
    } catch (error) {
      // If no previous balance sheet found, return 0
      return 0;
    }
  },

  // Phase 7: Quarter Transitions - Handle quarter feeding logic
  handleQuarterTransitions: async (
    organizationId: string,
    completedQuarter: string
  ) => {
    try {
      // Define quarter feeding mapping
      const quarterFeeding = {
        Q1: 'Q4',
        Q4: 'Q7',
        Q7: 'Q10',
        Q10: 'Q1',
        Q2: 'Q5',
        Q5: 'Q8',
        Q8: 'Q11',
        Q11: 'Q2',
        Q3: 'Q6',
        Q6: 'Q9',
        Q9: 'Q12',
        Q12: 'Q3',
      };

      const targetQuarter =
        quarterFeeding[completedQuarter as keyof typeof quarterFeeding];

      if (!targetQuarter) {
        throw new Error(`Invalid quarter name: ${completedQuarter}`);
      }

      // Get all quarterly-only users with positions in the completed quarter
      const { data: quarterlyUsers } = await supabase
        .from('user_profiles')
        .select('id, quarterly_capital')
        .eq('organization_id', organizationId)
        .eq('investment_status', 'quarterly_only')
        .gt('quarterly_capital', 0);

      if (!quarterlyUsers || quarterlyUsers.length === 0) {
        return {
          data: { transferredUsers: 0, totalAmountTransferred: 0 },
          error: null,
        };
      }

      // Get user quarterly positions for the completed quarter
      const { data: quarterPositions } = await supabase
        .from('user_quarterly_positions')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('quarter_name', completedQuarter)
        .in(
          'client_id',
          quarterlyUsers.map((u) => u.id)
        );

      const transfers = [];
      let totalAmountTransferred = 0;

      // Process each user's quarter transition
      for (const user of quarterlyUsers) {
        const userPosition = quarterPositions?.find(
          (p) => p.client_id === user.id
        );
        const transferAmount =
          userPosition?.closing_balance || user.quarterly_capital;

        if (transferAmount > 0) {
          // Create quarterly lineage record
          const { error: lineageError } = await supabase
            .from('quarterly_lineage')
            .insert({
              client_id: user.id,
              organization_id: organizationId,
              source_quarter_name: completedQuarter,
              target_quarter_name: targetQuarter,
              amount_transferred: transferAmount,
            });

          if (lineageError) {
            throw new Error(
              `Failed to create lineage record for user ${user.id}: ${lineageError.message}`
            );
          }

          // Create or update user quarterly position for target quarter
          const { error: positionError } = await supabase
            .from('user_quarterly_positions')
            .upsert({
              client_id: user.id,
              organization_id: organizationId,
              quarter_name: targetQuarter,
              opening_balance: transferAmount,
              closing_balance: transferAmount, // Will be updated during next cycle
              total_investments: 0,
              total_withdrawals: 0,
              total_pnl: 0,
              is_active: true,
            });

          if (positionError) {
            throw new Error(
              `Failed to update quarterly position for user ${user.id}: ${positionError.message}`
            );
          }

          transfers.push({
            userId: user.id,
            amount: transferAmount,
            sourceQuarter: completedQuarter,
            targetQuarter: targetQuarter,
          });

          totalAmountTransferred += transferAmount;
        }
      }

      return {
        data: {
          transferredUsers: transfers.length,
          totalAmountTransferred,
          transfers,
          sourceQuarter: completedQuarter,
          targetQuarter: targetQuarter,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 7: Update quarter snapshots
  updateQuarterSnapshots: async (
    organizationId: string,
    quarterName: string,
    finalCapital: number
  ) => {
    try {
      // Update the completed quarter snapshot
      const { error: updateError } = await supabase
        .from('quarter_snapshots')
        .update({
          total_capital_at_end: finalCapital,
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('organization_id', organizationId)
        .eq('quarter_name', quarterName);

      if (updateError) {
        throw new Error(
          `Failed to update quarter snapshot: ${updateError.message}`
        );
      }

      // Create next quarter snapshot
      const quarterFeeding = {
        Q1: 'Q4',
        Q4: 'Q7',
        Q7: 'Q10',
        Q10: 'Q1',
        Q2: 'Q5',
        Q5: 'Q8',
        Q8: 'Q11',
        Q11: 'Q2',
        Q3: 'Q6',
        Q6: 'Q9',
        Q9: 'Q12',
        Q12: 'Q3',
      };

      const nextQuarter =
        quarterFeeding[quarterName as keyof typeof quarterFeeding];
      if (nextQuarter) {
        const currentDate = new Date();
        const quarterStartDate = new Date(currentDate);
        const quarterEndDate = new Date(currentDate);
        quarterEndDate.setMonth(quarterEndDate.getMonth() + 3);

        const { error: createError } = await supabase
          .from('quarter_snapshots')
          .insert({
            organization_id: organizationId,
            quarter_name: nextQuarter,
            quarter_start_date: quarterStartDate.toISOString().split('T')[0],
            quarter_end_date: quarterEndDate.toISOString().split('T')[0],
            total_capital_at_start: finalCapital,
            total_capital_at_end: finalCapital,
            is_active: true,
          });

        if (createError) {
          throw new Error(
            `Failed to create next quarter snapshot: ${createError.message}`
          );
        }
      }

      return {
        data: {
          updatedQuarter: quarterName,
          nextQuarter: nextQuarter,
          finalCapital: finalCapital,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },

  // Phase 8: Cycle Completion
  completeCycle: async (
    organizationId: string,
    adminBalanceSheetId: string
  ) => {
    try {
      // Get admin balance sheet details
      const { data: adminBalanceSheet } = await supabase
        .from('admin_balance_sheets')
        .select('*')
        .eq('id', adminBalanceSheetId)
        .single();

      if (!adminBalanceSheet) {
        throw new Error('Admin balance sheet not found');
      }

      // Update organization completed_cycles
      const { data: organization } = await supabase
        .from('organizations')
        .select('completed_cycles')
        .eq('id', organizationId)
        .single();

      const newCompletedCycles = (organization?.completed_cycles || 0) + 1;

      const { error: orgUpdateError } = await supabase
        .from('organizations')
        .update({
          completed_cycles: newCompletedCycles,
          updated_at: new Date().toISOString(),
        })
        .eq('id', organizationId);

      if (orgUpdateError) {
        throw new Error(
          `Failed to update organization cycles: ${orgUpdateError.message}`
        );
      }

      // Get all users who participated in this cycle
      const { data: participatingUsers } = await supabase
        .from('client_balance_sheets')
        .select('client_id')
        .eq('admin_balance_sheet_id', adminBalanceSheetId)
        .eq('organization_id', organizationId);

      if (participatingUsers && participatingUsers.length > 0) {
        const userIds = participatingUsers.map((u) => u.client_id);

        // Update user completed_cycles for participating users
        const { error: userUpdateError } = await supabase
          .from('user_profiles')
          .update({
            completed_cycles: newCompletedCycles,
            updated_at: new Date().toISOString(),
          })
          .eq('organization_id', organizationId)
          .in('id', userIds);

        if (userUpdateError) {
          throw new Error(
            `Failed to update user cycles: ${userUpdateError.message}`
          );
        }
      }

      // Handle quarter transitions for quarterly distributions
      let quarterTransitionResult = null;
      if (
        adminBalanceSheet.period_type === 'quarterly' &&
        adminBalanceSheet.quarter_name
      ) {
        quarterTransitionResult =
          await adminBalanceSheetApi.handleQuarterTransitions(
            organizationId,
            adminBalanceSheet.quarter_name
          );

        if (quarterTransitionResult.error) {
          throw new Error(
            `Quarter transition failed: ${quarterTransitionResult.error.message}`
          );
        }

        // Update quarter snapshots
        const snapshotResult =
          await adminBalanceSheetApi.updateQuarterSnapshots(
            organizationId,
            adminBalanceSheet.quarter_name,
            adminBalanceSheet.total_capital_base
          );

        if (snapshotResult.error) {
          throw new Error(
            `Quarter snapshot update failed: ${snapshotResult.error.message}`
          );
        }
      }

      return {
        data: {
          organizationCycles: newCompletedCycles,
          participatingUsers: participatingUsers?.length || 0,
          quarterTransitions: quarterTransitionResult?.data,
          cycleType: adminBalanceSheet.period_type,
          quarterName: adminBalanceSheet.quarter_name,
        },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  },
};

// Transaction Window APIs
export const transactionWindowApi = {
  getActiveTransactionWindow: async (organizationId: string) => {
    const today = new Date().toISOString().split('T')[0];
    return supabase
      .from('transaction_windows')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('is_active', true)
      .lte('start_date', today)
      .gte('end_date', today)
      .single();
  },
  createTransactionWindow: async (windowData: {
    organization_id: string;
    start_date: string;
    end_date: string;
    is_active?: boolean;
  }) => {
    return supabase.from('transaction_windows').insert({
      ...windowData,
      is_active: windowData.is_active ?? true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
  },
  deactivateTransactionWindow: async (windowId: string) => {
    return supabase
      .from('transaction_windows')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', windowId);
  },
  getAllTransactionWindows: async (organizationId: string) => {
    return supabase
      .from('transaction_windows')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },
  closeTransactionWindow: async (organizationId: string) => {
    // This function will be called to close active windows and update balance sheets
    const { data: activeWindow } =
      await transactionWindowApi.getActiveTransactionWindow(organizationId);

    if (activeWindow) {
      // Deactivate the window
      await transactionWindowApi.deactivateTransactionWindow(activeWindow.id);

      // Get all approved investment and withdrawal requests during this window
      const { data: investmentRequests } = await supabase
        .from('investment_requests')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('status', 'approved')
        .gte('created_at', activeWindow.start_date)
        .lte('created_at', activeWindow.end_date);

      const { data: withdrawalRequests } = await supabase
        .from('withdrawal_requests')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('status', 'approved')
        .gte('created_at', activeWindow.start_date)
        .lte('created_at', activeWindow.end_date);

      // Update client balance sheets with investments and withdrawals
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();

      // Group requests by client
      const clientUpdates = new Map();

      // Process investment requests
      if (investmentRequests) {
        for (const request of investmentRequests) {
          const clientId = request.client_id;
          if (!clientUpdates.has(clientId)) {
            clientUpdates.set(clientId, { investments: 0, withdrawals: 0 });
          }
          clientUpdates.get(clientId).investments += request.amount;
        }
      }

      // Process withdrawal requests
      if (withdrawalRequests) {
        for (const request of withdrawalRequests) {
          const clientId = request.client_id;
          if (!clientUpdates.has(clientId)) {
            clientUpdates.set(clientId, { investments: 0, withdrawals: 0 });
          }
          clientUpdates.get(clientId).withdrawals += request.amount;
        }
      }

      // Update balance sheets for clients who made transactions during the window
      for (const [clientId, updates] of clientUpdates) {
        // Get user profile to check investment status
        const { data: userProfile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', clientId)
          .single();

        if (!userProfile) continue;

        const userInvestmentStatus = userProfile.investment_status;

        // Get the LATEST balance sheet for this user (most recent profit distribution)
        const { data: latestSheet } = await supabase
          .from('client_balance_sheets')
          .select('*')
          .eq('client_id', clientId)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (latestSheet) {
          const newClosingBalance =
            latestSheet.opening_balance +
            (latestSheet.pnl_earned || 0) +
            updates.investments -
            updates.withdrawals;

          // Update the LATEST balance sheet with investment/withdrawal data
          await supabase
            .from('client_balance_sheets')
            .update({
              investments_made: updates.investments,
              withdrawals_made: updates.withdrawals,
              closing_balance: newClosingBalance,
              updated_at: new Date().toISOString(),
            })
            .eq('id', latestSheet.id);

          // Update user profile withdrawal funds to the new closing balance
          await userProfileApi.updateUserProfile(clientId, {
            withdrawal_funds: newClosingBalance,
          });

          // Handle capital allocation based on user status
          if (userInvestmentStatus === 'new_month_1') {
            // For new_month_1 users, capital allocation was already handled during investment approval
            // No additional capital updates needed here
          } else {
            // For new_month_2 and quarterly_only users, capital allocation was handled during investment approval
            // No additional capital updates needed here during window closure
          }
        }
      }

      // Update investment status and increment completed_cycles for all clients
      const { data: allClients } = await supabase
        .from('user_profiles')
        .select(
          'id, investment_status, completed_cycles, monthly_capital, quarterly_capital'
        )
        .eq('organization_id', organizationId)
        .eq('role', 'client');

      if (allClients) {
        for (const client of allClients) {
          const newCompletedCycles = (client.completed_cycles || 0) + 1;

          // Only update status if client has completed at least one cycle BEFORE this increment
          if (client.completed_cycles > 0) {
            let newStatus = client.investment_status;
            let monthlyCapitalUpdate = client.monthly_capital || 0;
            let quarterlyCapitalUpdate = client.quarterly_capital || 0;
            let orgMonthlyDeduction = 0;
            let orgQuarterlyAddition = 0;

            if (client.investment_status === 'new_month_1') {
              newStatus = 'new_month_2';
              // For new_month_1 -> new_month_2: Split closing balance 50/50
              const { data: latestBalanceSheet } = await supabase
                .from('client_balance_sheets')
                .select('closing_balance')
                .eq('client_id', client.id)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

              if (latestBalanceSheet) {
                const closingBalance = latestBalanceSheet.closing_balance;
                const halfBalance = closingBalance / 2;

                // Update capital allocation: 50% to quarterly, 50% to monthly
                quarterlyCapitalUpdate = halfBalance;
                monthlyCapitalUpdate = halfBalance;

                // Calculate organization capital changes
                const oldQuarterly = client.quarterly_capital || 0;
                const oldMonthly = client.monthly_capital || 0;
                orgQuarterlyAddition = quarterlyCapitalUpdate - oldQuarterly;
                orgMonthlyDeduction = oldMonthly - monthlyCapitalUpdate;
              }
            } else if (client.investment_status === 'new_month_2') {
              newStatus = 'quarterly_only';
              // For new_month_2 -> quarterly_only: Move all closing balance to quarterly
              const { data: latestBalanceSheet } = await supabase
                .from('client_balance_sheets')
                .select('closing_balance')
                .eq('client_id', client.id)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

              if (latestBalanceSheet) {
                const closingBalance = latestBalanceSheet.closing_balance;

                quarterlyCapitalUpdate = closingBalance;
                monthlyCapitalUpdate = 0;

                // Calculate organization capital changes
                const oldQuarterly = client.quarterly_capital || 0;
                const oldMonthly = client.monthly_capital || 0;
                orgQuarterlyAddition = quarterlyCapitalUpdate - oldQuarterly;
                orgMonthlyDeduction = oldMonthly; // Remove all monthly capital
              }
            } else if (client.investment_status === 'quarterly_only') {
              // For quarterly_only users, update their quarterly capital to latest closing balance
              const { data: latestBalanceSheet } = await supabase
                .from('client_balance_sheets')
                .select('closing_balance')
                .eq('client_id', client.id)
                .order('period_year', { ascending: false })
                .order('period_month', { ascending: false })
                .limit(1)
                .single();

              if (latestBalanceSheet) {
                const newQuarterlyCapital = latestBalanceSheet.closing_balance;
                const capitalDifference =
                  newQuarterlyCapital - (client.quarterly_capital || 0);

                quarterlyCapitalUpdate = newQuarterlyCapital;
                orgQuarterlyAddition = capitalDifference;
              }
            }
            // quarterly_only status remains unchanged

            // Update client status, capital allocation, and increment completed_cycles
            await supabase
              .from('user_profiles')
              .update({
                investment_status: newStatus,
                completed_cycles: newCompletedCycles,
                monthly_capital: monthlyCapitalUpdate,
                quarterly_capital: quarterlyCapitalUpdate,
              })
              .eq('id', client.id);

            // Update organization capital if there are changes
            if (orgMonthlyDeduction > 0 || orgQuarterlyAddition !== 0) {
              // Get current organization capital
              const { data: orgData } = await organizationApi.getOrganization(
                organizationId
              );
              if (orgData) {
                await organizationApi.updateOrganization(organizationId, {
                  total_capital_monthly:
                    (orgData.total_capital_monthly || 0) - orgMonthlyDeduction,
                  total_capital_quarterly:
                    (orgData.total_capital_quarterly || 0) +
                    orgQuarterlyAddition,
                });
              }
            }
          } else {
            // Just increment completed_cycles without changing status
            await supabase
              .from('user_profiles')
              .update({
                completed_cycles: newCompletedCycles,
              })
              .eq('id', client.id);
          }
        }
      }

      return {
        success: true,
        message:
          'Transaction window closed, balance sheets updated, and client statuses progressed',
      };
    }

    return { success: false, message: 'No active transaction window found' };
  },
};

// Client Balance Sheet APIs
export const clientBalanceSheetApi = {
  getClientBalanceSheets: async (clientId: string) => {
    return supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', clientId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false });
  },
  getLatestClientBalanceSheet: async (clientId: string) => {
    return supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', clientId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false })
      .limit(1)
      .single();
  },
  createClientBalanceSheet: async (balanceSheetData: {
    client_id: string;
    organization_id: string;
    admin_balance_sheet_id: string;
    period_type: string;
    period_month: number;
    period_year: number;
    opening_balance: number;
    investments_made?: number;
    pnl_earned?: number;
    withdrawals_made?: number;
    closing_balance: number;
    monthly_capital_used?: number;
    quarterly_capital_used?: number;
    profit_rate_applied: number;
    user_status_at_creation: string;
  }) => {
    return supabase
      .from('client_balance_sheets')
      .insert({
        ...balanceSheetData,
        investments_made: balanceSheetData.investments_made ?? 0,
        pnl_earned: balanceSheetData.pnl_earned ?? 0,
        withdrawals_made: balanceSheetData.withdrawals_made ?? 0,
        monthly_capital_used: balanceSheetData.monthly_capital_used ?? 0,
        quarterly_capital_used: balanceSheetData.quarterly_capital_used ?? 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();
  },
  updateClientBalanceSheet: async (
    id: string,
    updates: {
      investments_made?: number;
      withdrawals_made?: number;
      pnl_earned?: number;
      closing_balance?: number;
      monthly_capital_used?: number;
      quarterly_capital_used?: number;
    }
  ) => {
    return supabase
      .from('client_balance_sheets')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);
  },
  getClientBalanceSheetsByAdminSheet: async (adminBalanceSheetId: string) => {
    return supabase
      .from('client_balance_sheets')
      .select(
        `
        *,
        client:user_profiles!client_id(*)
      `
      )
      .eq('admin_balance_sheet_id', adminBalanceSheetId)
      .order('created_at', { ascending: false });
  },
};

// Quarterly Tracking APIs
export const quarterlyTrackingApi = {
  createQuarterlyPosition: async (positionData: {
    client_id: string;
    organization_id: string;
    quarter_name: string;
    principal_amount: number;
    accumulated_profit?: number;
    current_balance: number;
    start_date: string;
    successor_quarter?: string | null;
  }) => {
    return supabase
      .from('user_quarterly_positions')
      .insert({
        ...positionData,
        accumulated_profit: positionData.accumulated_profit ?? 0,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();
  },

  getActiveQuarterlyPositions: async (clientId: string) => {
    return supabase
      .from('user_quarterly_positions')
      .select('*')
      .eq('client_id', clientId)
      .eq('is_active', true)
      .order('start_date', { ascending: false });
  },

  updateQuarterlyPosition: async (
    positionId: string,
    updates: {
      accumulated_profit?: number;
      current_balance?: number;
      end_date?: string | null;
      is_active?: boolean;
    }
  ) => {
    return supabase
      .from('user_quarterly_positions')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', positionId);
  },

  getQuarterlyPositionByQuarter: async (
    clientId: string,
    quarterName: string
  ) => {
    return supabase
      .from('user_quarterly_positions')
      .select('*')
      .eq('client_id', clientId)
      .eq('quarter_name', quarterName)
      .eq('is_active', true)
      .single();
  },

  transitionQuarterlyPosition: async (
    currentPositionId: string,
    newQuarterData: {
      client_id: string;
      organization_id: string;
      quarter_name: string;
      principal_amount: number;
      start_date: string;
      successor_quarter?: string | null;
    }
  ) => {
    // End current position
    await supabase
      .from('user_quarterly_positions')
      .update({
        end_date: new Date().toISOString().split('T')[0],
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', currentPositionId);

    // Create new position
    return supabase
      .from('user_quarterly_positions')
      .insert({
        ...newQuarterData,
        accumulated_profit: 0,
        current_balance: newQuarterData.principal_amount,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();
  },
};

// User Profile APIs
export const userProfileApi = {
  updateUserProfile: async (
    userId: string,
    updates: {
      investment_status?: string;
      total_investment?: number;
      total_profit?: number;
      withdrawal_funds?: number;
      split_cycle_start_date?: string;
      split_cycle_completed_date?: string;
    }
  ) => {
    return supabase
      .from('user_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);
  },
};

// Dashboard APIs
export const dashboardApi = {
  getClientOverview: async (userId: string) => {
    return supabase
      .from('client_overview')
      .select('*')
      .eq('user_id', userId)
      .single();
  },
  getAdminOverview: async (organizationId: string) => {
    return supabase
      .from('admin_overview')
      .select('*')
      .eq('organization_id', organizationId)
      .single();
  },
};
