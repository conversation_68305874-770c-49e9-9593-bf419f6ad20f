import { CapacitorConfig } from '@capacitor/core';

const config: CapacitorConfig = {
  appId: 'com.maven.investments',
  appName: 'MavenI',
  webDir: 'dist',
  // Removed server config to use local app instead of remote URL
  plugins: {
    StatusBar: {
      style: 'DARK',
      overlaysWebView: false,
    },
    SafeArea: {
      enabled: true,
      customColorsForSystemBars: true,
      statusBarColor: '#000000',
    },
  },
};

export default config;
