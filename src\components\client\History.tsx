import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useAuth } from '@/contexts/AuthContext';
import { useClientBalanceSheets } from '@/hooks/useApi';
import { Loader2 } from 'lucide-react';
import { MONTHS, QUARTERS, formatCurrency } from '@/lib/constants';

interface BalanceSheetData {
  id: string;
  admin_balance_sheet_id: string;
  period_type: string; // 'monthly' or 'quarterly'
  period_month: number;
  period_year: number;
  opening_balance: number;
  investments_made: number;
  pnl_earned: number;
  withdrawals_made: number;
  closing_balance: number;
  monthly_capital_used: number;
  quarterly_capital_used: number;
  profit_rate_applied: number;
  user_status_at_creation: string;
  created_at: string;
}

interface HistoryProps {
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
}

const History: React.FC<HistoryProps> = ({
  currentPage,
  setCurrentPage,
  itemsPerPage,
}) => {
  const { user } = useAuth();
  const {
    data: balanceSheetData,
    isLoading,
    error,
  } = useClientBalanceSheets(user?.id || '');

  // Helper function to get month name
  const getMonthName = (monthNumber: number) => {
    const month = MONTHS.find((m) => m.number === monthNumber);
    return month ? month.label : `M${monthNumber}`;
  };

  const getPeriodLabel = (periodType: string, monthNumber: number) => {
    if (periodType === 'quarterly') {
      const quarterId = `Q${monthNumber}`;
      const quarter = QUARTERS.find((q) => q.id === quarterId);
      return quarter ? quarter.label : quarterId;
    }

    const month = MONTHS.find((m) => m.number === monthNumber);
    return month ? month.label : `M${monthNumber}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet History</h2>
        <Card>
          <CardContent className="p-6 flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading balance sheets...
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet History</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              Failed to load balance sheet data. Please try again later.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const balanceSheets = balanceSheetData?.data || [];

  if (balanceSheets.length === 0) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Balance Sheet History</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              No balance sheet records found. Balance sheets will appear here
              after profit distributions.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const totalPages = Math.ceil(balanceSheets.length / itemsPerPage);
  const paginatedBalanceSheets = balanceSheets.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Balance Sheet History</h2>
      <Card>
        <CardHeader>
          <CardTitle>Balance Sheet History</CardTitle>
          <p className="text-sm text-muted-foreground">
            Enhanced balance sheets showing capital allocation and profit
            distribution details
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Period</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Opening Balance</TableHead>
                <TableHead>Investments</TableHead>
                <TableHead>P&L Earned</TableHead>
                <TableHead>Withdrawals</TableHead>
                <TableHead>Closing Balance</TableHead>
                <TableHead>Capital Used</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBalanceSheets.map((sheet: BalanceSheetData) => (
                <TableRow key={sheet.id}>
                  <TableCell>
                    <div>
                      <Badge
                        variant="outline"
                        className={
                          sheet.period_type === 'monthly'
                            ? 'bg-blue-50'
                            : 'bg-green-50'
                        }
                      >
                        {getPeriodLabel(sheet.period_type, sheet.period_month)}
                      </Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {sheet.period_year}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        sheet.period_type === 'monthly'
                          ? 'default'
                          : 'secondary'
                      }
                    >
                      {sheet.period_type === 'monthly'
                        ? 'Monthly'
                        : 'Quarterly'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {formatCurrency(
                      sheet.period_type === 'monthly'
                        ? sheet.monthly_capital_used
                        : sheet.quarterly_capital_used
                    )}
                  </TableCell>
                  <TableCell className="text-blue-600">
                    {sheet.investments_made > 0
                      ? `+${formatCurrency(sheet.investments_made)}`
                      : '-'}
                  </TableCell>
                  <TableCell className="text-green-600">
                    {sheet.pnl_earned > 0
                      ? `+${formatCurrency(sheet.pnl_earned)}`
                      : '-'}
                  </TableCell>
                  <TableCell className="text-red-600">
                    {sheet.withdrawals_made > 0
                      ? `-${formatCurrency(sheet.withdrawals_made)}`
                      : '-'}
                  </TableCell>
                  <TableCell className="font-bold">
                    {formatCurrency(sheet.closing_balance)}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {sheet.monthly_capital_used > 0 && (
                        <div className="text-blue-600">
                          Monthly: {formatCurrency(sheet.monthly_capital_used)}
                        </div>
                      )}
                      {sheet.quarterly_capital_used > 0 && (
                        <div className="text-green-600">
                          Quarterly:{' '}
                          {formatCurrency(sheet.quarterly_capital_used)}
                        </div>
                      )}
                      {sheet.monthly_capital_used === 0 &&
                        sheet.quarterly_capital_used === 0 && (
                          <span className="text-muted-foreground">-</span>
                        )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <Badge variant="outline" className="mb-1">
                        {sheet.user_status_at_creation
                          .replace('_', ' ')
                          .toUpperCase()}
                      </Badge>
                      <div className="text-xs text-muted-foreground">
                        Rate: {(sheet.profit_rate_applied * 100).toFixed(4)}%
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                      />
                    </PaginationItem>
                  )}
                  {Array.from({ length: totalPages }).map((_, i) => (
                    <PaginationItem key={i}>
                      <PaginationLink
                        onClick={() => setCurrentPage(i + 1)}
                        isActive={currentPage === i + 1}
                      >
                        {i + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationNext
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default History;
