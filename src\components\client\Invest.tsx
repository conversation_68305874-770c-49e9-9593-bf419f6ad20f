/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowUpCircle, ArrowDownCircle, AlertCircle } from 'lucide-react';
import {
  useUserProfile,
  useInvestmentRequests,
  useWithdrawalRequests,
  useCreateInvestmentRequest,
  useCreateWithdrawalRequest,
  useActiveTransactionWindowForUser,
} from '@/hooks/useApi';

interface InvestProps {
  availableForWithdrawal: number;
}

const Invest: React.FC<InvestProps> = ({ availableForWithdrawal }) => {
  const { user } = useAuth();

  // React Query hooks
  const { data: userProfileData } = useUserProfile(user?.id || '');
  const { data: investmentRequestsData, isLoading: investmentLoading } =
    useInvestmentRequests(user?.id || '');
  const { data: withdrawalRequestsData, isLoading: withdrawalLoading } =
    useWithdrawalRequests(user?.id || '');
  const { data: activeWindowData } = useActiveTransactionWindowForUser(
    user?.organization_id || '',
    user?.id || ''
  );
  console.log(activeWindowData);
  const createInvestmentRequest = useCreateInvestmentRequest();
  const createWithdrawalRequest = useCreateWithdrawalRequest();

  // Extract data from React Query responses
  const userProfile = userProfileData?.data;
  const investmentRequests = investmentRequestsData?.data || [];
  const withdrawalRequests = withdrawalRequestsData?.data || [];
  const activeWindow = activeWindowData?.data
  const isWindowActive = !!activeWindow;
  const isLoading = investmentLoading || withdrawalLoading;

  // Investment form state
  const [investAmount, setInvestAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'upi' | 'bank_transfer'>(
    'upi'
  );
  const [isInvestDialogOpen, setIsInvestDialogOpen] = useState(false);

  // Withdrawal form state
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawalMethod, setWithdrawalMethod] = useState<
    'upi' | 'bank_transfer'
  >('upi');
  const [upiId, setUpiId] = useState('');
  const [bankName, setBankName] = useState('');
  const [bankIfsc, setBankIfsc] = useState('');
  const [bankAccountNumber, setBankAccountNumber] = useState('');
  const [bankAccountHolder, setBankAccountHolder] = useState('');
  const [isWithdrawDialogOpen, setIsWithdrawDialogOpen] = useState(false);

  const handleInvestRequest = () => {
    const amount = parseFloat(investAmount);
    if (!amount || amount <= 0) {
      return; // React Query mutation will handle the error
    }

    createInvestmentRequest.mutate(
      {
        client_id: user!.id,
        organization_id: user!.organization_id,
        amount: amount,
        payment_method: paymentMethod,
      },
      {
        onSuccess: () => {
          setInvestAmount('');
          setIsInvestDialogOpen(false);
        },
      }
    );
  };

  const handleWithdrawRequest = () => {
    const amount = parseFloat(withdrawAmount);
    if (!amount || amount <= 0) {
      return; // React Query mutation will handle validation
    }

    if (amount > availableForWithdrawal) {
      return; // React Query mutation will handle validation
    }

    // Check if user has required payment details
    const hasUpiDetails = userProfile?.upi_id;
    const hasBankDetails =
      userProfile?.bank_name &&
      userProfile?.bank_ifsc &&
      userProfile?.bank_account_number;

    if (withdrawalMethod === 'upi' && !hasUpiDetails && !upiId) {
      return; // React Query mutation will handle validation
    }

    if (
      withdrawalMethod === 'bank_transfer' &&
      !hasBankDetails &&
      (!bankName || !bankIfsc || !bankAccountNumber || !bankAccountHolder)
    ) {
      return; // React Query mutation will handle validation
    }

    const requestData: any = {
      client_id: user!.id,
      organization_id: user!.organization_id,
      amount: amount,
      withdrawal_method: withdrawalMethod,
    };

    // Add payment details based on method
    if (withdrawalMethod === 'upi') {
      requestData.upi_id = upiId || userProfile?.upi_id;
    } else {
      requestData.bank_name = bankName || userProfile?.bank_name;
      requestData.bank_ifsc = bankIfsc || userProfile?.bank_ifsc;
      requestData.bank_account_number =
        bankAccountNumber || userProfile?.bank_account_number;
      requestData.bank_account_holder =
        bankAccountHolder || userProfile?.bank_account_holder;
    }

    createWithdrawalRequest.mutate(requestData, {
      onSuccess: () => {
        setWithdrawAmount('');
        setUpiId('');
        setBankName('');
        setBankIfsc('');
        setBankAccountNumber('');
        setBankAccountHolder('');
        setIsWithdrawDialogOpen(false);
      },
    });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Investments & Withdrawals</h2>

      {/* Transaction Window Status */}
      {!isWindowActive && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800 dark:text-orange-200">
                  Transaction Window Closed
                </p>
                <p className="text-sm text-orange-600 dark:text-orange-300">
                  Investment and withdrawal requests can only be submitted
                  during active transaction windows. Please wait for the next
                  window to open after profit distribution.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isWindowActive && activeWindow && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <div>
                <p className="font-medium text-green-800 dark:text-green-200">
                  {activeWindow.period_type === 'monthly'
                    ? 'Monthly'
                    : 'Quarterly'}{' '}
                  Transaction Window Active
                </p>
                <p className="text-sm text-green-600 dark:text-green-300">
                  You can submit investment and withdrawal requests until{' '}
                  {new Date(activeWindow.window_end_date).toLocaleDateString()}.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="invest" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="invest">Investment Requests</TabsTrigger>
          <TabsTrigger value="withdraw">Withdrawal Requests</TabsTrigger>
        </TabsList>
        <TabsContent value="invest" className="space-y-4">
          <div className="flex justify-end">
            <Dialog
              open={isInvestDialogOpen}
              onOpenChange={setIsInvestDialogOpen}
            >
              <DialogTrigger asChild>
                <Button disabled={!isWindowActive}>
                  <ArrowUpCircle className="h-4 w-4 mr-2" />
                  New Investment Request
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>New Investment Request</DialogTitle>
                  <DialogDescription>
                    Submit a request for a new investment to your portfolio
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="invest-amount">Investment Amount (₹)</Label>
                    <Input
                      id="invest-amount"
                      type="number"
                      placeholder="Enter amount"
                      value={investAmount}
                      onChange={(e) => setInvestAmount(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="payment-method">Payment Method</Label>
                    <Select
                      value={paymentMethod}
                      onValueChange={(value: 'upi' | 'bank_transfer') =>
                        setPaymentMethod(value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="upi">UPI</SelectItem>
                        <SelectItem value="bank_transfer">
                          Bank Transfer
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={handleInvestRequest} className="w-full">
                    Submit Investment Request
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Admin Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {investmentRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        {new Date(request.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-semibold text-blue-600">
                        ₹{request.amount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {request.payment_method === 'upi'
                            ? 'UPI'
                            : 'Bank Transfer'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(request.status)}>
                          {request.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {request.admin_notes || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                  {investmentRequests.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        No investment requests found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="withdraw" className="space-y-4">
          <div className="flex justify-end">
            <Dialog
              open={isWithdrawDialogOpen}
              onOpenChange={setIsWithdrawDialogOpen}
            >
              <DialogTrigger asChild>
                <Button disabled={!isWindowActive}>
                  <ArrowDownCircle className="h-4 w-4 mr-2" />
                  New Withdrawal Request
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>New Withdrawal Request</DialogTitle>
                  <DialogDescription>
                    Submit a request to withdraw funds from your portfolio
                  </DialogDescription>
                </DialogHeader>

                {/* Available Funds Display */}
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Available for Withdrawal
                      </p>
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        ₹{availableForWithdrawal.toLocaleString()}
                      </p>
                    </div>
                    <ArrowDownCircle className="h-8 w-8 text-blue-500" />
                  </div>
                </div>

                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="withdraw-amount">
                      Withdrawal Amount (₹)
                    </Label>
                    <Input
                      id="withdraw-amount"
                      type="number"
                      placeholder="Enter amount"
                      max={availableForWithdrawal}
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                    />
                    {parseFloat(withdrawAmount) > availableForWithdrawal && (
                      <p className="text-xs text-red-600">
                        Amount exceeds available funds (₹
                        {availableForWithdrawal.toLocaleString()})
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="withdrawal-method">Withdrawal Method</Label>
                    <Select
                      value={withdrawalMethod}
                      onValueChange={(value: 'upi' | 'bank_transfer') =>
                        setWithdrawalMethod(value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select withdrawal method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="upi">UPI</SelectItem>
                        <SelectItem value="bank_transfer">
                          Bank Transfer
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {withdrawalMethod === 'upi' && !userProfile?.upi_id && (
                    <div className="space-y-2">
                      <Label htmlFor="upi-id">UPI ID</Label>
                      <Input
                        id="upi-id"
                        type="text"
                        placeholder="Enter your UPI ID"
                        value={upiId}
                        onChange={(e) => setUpiId(e.target.value)}
                      />
                    </div>
                  )}

                  {withdrawalMethod === 'bank_transfer' &&
                    !userProfile?.bank_name && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="bank-name">Bank Name</Label>
                          <Input
                            id="bank-name"
                            type="text"
                            placeholder="Enter bank name"
                            value={bankName}
                            onChange={(e) => setBankName(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bank-ifsc">IFSC Code</Label>
                          <Input
                            id="bank-ifsc"
                            type="text"
                            placeholder="Enter IFSC code"
                            value={bankIfsc}
                            onChange={(e) => setBankIfsc(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bank-account">Account Number</Label>
                          <Input
                            id="bank-account"
                            type="text"
                            placeholder="Enter account number"
                            value={bankAccountNumber}
                            onChange={(e) =>
                              setBankAccountNumber(e.target.value)
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="account-holder">
                            Account Holder Name
                          </Label>
                          <Input
                            id="account-holder"
                            type="text"
                            placeholder="Enter account holder name"
                            value={bankAccountHolder}
                            onChange={(e) =>
                              setBankAccountHolder(e.target.value)
                            }
                          />
                        </div>
                      </>
                    )}

                  {userProfile?.upi_id && withdrawalMethod === 'upi' && (
                    <div className="p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-green-600" />
                        <p className="text-sm text-green-600">
                          Using saved UPI ID: {userProfile.upi_id}
                        </p>
                      </div>
                    </div>
                  )}

                  {userProfile?.bank_name &&
                    withdrawalMethod === 'bank_transfer' && (
                      <div className="p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-green-600" />
                          <p className="text-sm text-green-600">
                            Using saved bank details: {userProfile.bank_name}
                          </p>
                        </div>
                      </div>
                    )}

                  <Button onClick={handleWithdrawRequest} className="w-full">
                    Submit Withdrawal Request
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Withdrawal Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Admin Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {withdrawalRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        {new Date(request.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-semibold text-red-600">
                        ₹{request.amount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {request.withdrawal_method === 'upi'
                            ? 'UPI'
                            : 'Bank Transfer'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(request.status)}>
                          {request.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {request.admin_notes || '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                  {withdrawalRequests.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        No withdrawal requests found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Invest;
