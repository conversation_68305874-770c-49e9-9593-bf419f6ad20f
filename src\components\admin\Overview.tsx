/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface OverviewProps {
  totalCapital: number;
  clients: any[];
  pendingRequests: any[];
  organizationData?: any;
}

const Overview: React.FC<OverviewProps> = ({
  totalCapital,
  clients,
  pendingRequests,
  organizationData,
}) => {
  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-500/20">
        <CardHeader>
          <CardTitle className="text-2xl text-blue-600">
            Total Capital Invested
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-3xl font-bold mb-4">
            ₹{totalCapital.toLocaleString()}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white/50 dark:bg-gray-800/50 p-3 rounded">
              <p className="text-gray-600 dark:text-gray-400">
                Monthly Capital
              </p>
              <p className="text-xl font-semibold text-blue-600">
                ₹
                {(
                  organizationData?.total_capital_monthly || 0
                ).toLocaleString()}
              </p>
            </div>
            <div className="bg-white/50 dark:bg-gray-800/50 p-3 rounded">
              <p className="text-gray-600 dark:text-gray-400">
                Quarterly Capital
              </p>
              <p className="text-xl font-semibold text-purple-600">
                ₹
                {(
                  organizationData?.total_capital_quarterly || 0
                ).toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Clients</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{clients.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Pending Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{pendingRequests.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Maven Profit</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-orange-600">
              ₹{(organizationData?.total_maven_profit || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Client Profit</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600">
              ₹{(organizationData?.total_client_profit || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Overview;
