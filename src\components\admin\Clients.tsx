/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { UserPlus, Loader2 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import {
  useOrganizationClients,
  useCreateClient,
  useCreateBalanceSheet,
} from '@/hooks/useApi';

const Clients: React.FC = () => {
  const { user } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const [newClient, setNewClient] = useState({
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    password: '',
    is_existing_user: false,
    // Balance sheet fields for existing users
    opening_balance: 0,
    investments_made: 0,
    pnl_earned: 0,
    withdrawals_made: 0,
    closing_balance: 0,
  });

  const itemsPerPage = 5;

  const {
    data: clientsData,
    isLoading,
    error,
  } = useOrganizationClients(user?.organization_id || '');

  const createClientMutation = useCreateClient();
  const createBalanceSheetMutation = useCreateBalanceSheet();

  const clients = clientsData?.data || [];

  const handleCreateClient = () => {
    if (
      !newClient.email ||
      !newClient.first_name ||
      !newClient.last_name ||
      !newClient.password
    ) {
      return;
    }

    if (!user) return;

    createClientMutation.mutate(
      {
        email: newClient.email,
        first_name: newClient.first_name,
        last_name: newClient.last_name,
        phone: newClient.phone || undefined,
        password: newClient.password,
        organization_id: user.organization_id,
        is_existing_user: newClient.is_existing_user,
      },
      {
        onSuccess: (data) => {
          // Create balance sheet for the new client
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
          const currentYear = currentDate.getFullYear();

          const balanceSheetData = {
            client_id: data.data.profile.id,
            organization_id: user.organization_id,
            period_month: currentMonth,
            period_year: currentYear,
            opening_balance: newClient.is_existing_user
              ? newClient.opening_balance
              : 0,
            investments_made: newClient.is_existing_user
              ? newClient.investments_made
              : 0,
            pnl_earned: newClient.is_existing_user ? newClient.pnl_earned : 0,
            withdrawals_made: newClient.is_existing_user
              ? newClient.withdrawals_made
              : 0,
            closing_balance: newClient.is_existing_user
              ? newClient.closing_balance
              : 0,
          };

          createBalanceSheetMutation.mutate(balanceSheetData, {
            onSuccess: () => {
              setNewClient({
                email: '',
                first_name: '',
                last_name: '',
                phone: '',
                password: '',
                is_existing_user: false,
                opening_balance: 0,
                investments_made: 0,
                pnl_earned: 0,
                withdrawals_made: 0,
                closing_balance: 0,
              });
            },
          });
        },
      }
    );
  };
  const paginatedClients = clients.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(clients.length / itemsPerPage);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Client Management</h2>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading clients...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Client Management</h2>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              Error loading clients. Please try again.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Client Management</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="w-4 h-4 mr-2" />
              Add Client
            </Button>
          </DialogTrigger>
          <DialogContent className="max-h-[90vh] overflow-y-auto max-w-[95vw] sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Create New Client Account</DialogTitle>
              <DialogDescription>
                Add a new client/investor to the system
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 pb-4">
              <div className="space-y-2">
                <Label htmlFor="client-email">Email *</Label>
                <Input
                  id="client-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newClient.email}
                  onChange={(e) =>
                    setNewClient({ ...newClient, email: e.target.value })
                  }
                  className="h-12 sm:h-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client-first-name">First Name *</Label>
                <Input
                  id="client-first-name"
                  placeholder="John"
                  value={newClient.first_name}
                  onChange={(e) =>
                    setNewClient({ ...newClient, first_name: e.target.value })
                  }
                  className="h-12 sm:h-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client-last-name">Last Name *</Label>
                <Input
                  id="client-last-name"
                  placeholder="Doe"
                  value={newClient.last_name}
                  onChange={(e) =>
                    setNewClient({ ...newClient, last_name: e.target.value })
                  }
                  className="h-12 sm:h-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client-phone">Phone (Optional)</Label>
                <Input
                  id="client-phone"
                  placeholder="+1234567890"
                  value={newClient.phone}
                  onChange={(e) =>
                    setNewClient({ ...newClient, phone: e.target.value })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="client-password">Password *</Label>
                <Input
                  id="client-password"
                  type="password"
                  placeholder="Enter password"
                  value={newClient.password}
                  onChange={(e) =>
                    setNewClient({ ...newClient, password: e.target.value })
                  }
                  className="h-12 sm:h-10"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="existing-user"
                  checked={newClient.is_existing_user}
                  onCheckedChange={(checked) =>
                    setNewClient({ ...newClient, is_existing_user: !!checked })
                  }
                />
                <Label htmlFor="existing-user" className="text-sm">
                  Mark as existing user (Quarterly Only status)
                </Label>
              </div>

              {newClient.is_existing_user && (
                <div className="space-y-4 border-t pt-4">
                  <h4 className="text-sm font-medium">
                    Balance Sheet Information
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="opening-balance">Opening Balance</Label>
                      <Input
                        id="opening-balance"
                        type="number"
                        placeholder="0"
                        value={newClient.opening_balance}
                        onChange={(e) =>
                          setNewClient({
                            ...newClient,
                            opening_balance: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="investments-made">Investments Made</Label>
                      <Input
                        id="investments-made"
                        type="number"
                        placeholder="0"
                        value={newClient.investments_made}
                        onChange={(e) =>
                          setNewClient({
                            ...newClient,
                            investments_made: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pnl-earned">P&L Earned</Label>
                      <Input
                        id="pnl-earned"
                        type="number"
                        placeholder="0"
                        value={newClient.pnl_earned}
                        onChange={(e) =>
                          setNewClient({
                            ...newClient,
                            pnl_earned: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="withdrawals-made">Withdrawals Made</Label>
                      <Input
                        id="withdrawals-made"
                        type="number"
                        placeholder="0"
                        value={newClient.withdrawals_made}
                        onChange={(e) =>
                          setNewClient({
                            ...newClient,
                            withdrawals_made: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-2 col-span-1 sm:col-span-2">
                      <Label htmlFor="closing-balance">Closing Balance</Label>
                      <Input
                        id="closing-balance"
                        type="number"
                        placeholder="0"
                        value={newClient.closing_balance}
                        onChange={(e) =>
                          setNewClient({
                            ...newClient,
                            closing_balance: parseFloat(e.target.value) || 0,
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              )}
              <Button
                onClick={handleCreateClient}
                className="w-full h-12 sm:h-10"
                disabled={
                  createClientMutation.isPending ||
                  createBalanceSheetMutation.isPending
                }
              >
                {createClientMutation.isPending ||
                createBalanceSheetMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {createClientMutation.isPending
                      ? 'Creating Client...'
                      : 'Creating Balance Sheet...'}
                  </>
                ) : (
                  'Create Client Account'
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="p-3 sm:p-6">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="hidden sm:table-cell">Phone</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedClients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell className="font-medium">
                      {client.email}
                    </TableCell>
                    <TableCell>
                      {client.first_name} {client.last_name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {client.investment_status
                          .replace('_', ' ')
                          .toUpperCase()}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {client.phone || 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">
                        <span className="hidden sm:inline">View Details</span>
                        <span className="sm:hidden">View</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {paginatedClients.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No clients found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {totalPages > 1 && (
              <Pagination className="mt-4">
                <PaginationContent>
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                      />
                    </PaginationItem>
                  )}
                  {Array.from({ length: totalPages }).map((_, i) => (
                    <PaginationItem key={i}>
                      <PaginationLink
                        isActive={currentPage === i + 1}
                        onClick={() => setCurrentPage(i + 1)}
                      >
                        {i + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationNext
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                      />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Clients;
