/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';

interface ProfileProps {
  user: any;
  userProfile: any;
  currentQuarter: string;
}

const Profile: React.FC<ProfileProps> = ({
  user,
  userProfile,
  currentQuarter,
}) => {
  const totalInvestment = userProfile?.total_investment || 0;
  const totalProfit = userProfile?.total_profit || 0;
  const currentBalance = totalInvestment + totalProfit;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Profile</h2>
      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Name</Label>
              <p className="text-lg font-medium">{user?.name}</p>
            </div>
            <div>
              <Label>Client ID</Label>
              <p className="text-lg font-medium">{user?.id}</p>
            </div>
            <div>
              <Label>Role</Label>
              <p className="text-lg font-medium capitalize">{user?.role}</p>
            </div>
            <div>
              <Label>Current Quarter</Label>
              <Badge variant="default">{currentQuarter.toUpperCase()}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Portfolio Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">Total Investment</p>
              <p className="text-2xl font-bold text-blue-600">
                ₹{totalInvestment.toLocaleString()}
              </p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">Total Profit</p>
              <p className="text-2xl font-bold text-green-600">
                ₹{totalProfit.toLocaleString()}
              </p>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">Current Balance</p>
              <p className="text-2xl font-bold">
                ₹{currentBalance.toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Profile;
