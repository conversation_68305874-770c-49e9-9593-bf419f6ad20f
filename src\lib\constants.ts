// Investment periods and cycles

export const MONTHS = [
  { id: 'm1', label: 'M1 (January)', month: 'January', number: 1 },
  { id: 'm2', label: 'M2 (February)', month: 'February', number: 2 },
  { id: 'm3', label: 'M3 (March)', month: 'March', number: 3 },
  { id: 'm4', label: 'M4 (April)', month: 'April', number: 4 },
  { id: 'm5', label: 'M5 (May)', month: 'May', number: 5 },
  { id: 'm6', label: 'M6 (June)', month: 'June', number: 6 },
  { id: 'm7', label: 'M7 (July)', month: 'July', number: 7 },
  { id: 'm8', label: 'M8 (August)', month: 'August', number: 8 },
  { id: 'm9', label: 'M9 (September)', month: 'September', number: 9 },
  { id: 'm10', label: 'M10 (October)', month: 'October', number: 10 },
  { id: 'm11', label: 'M11 (November)', month: 'November', number: 11 },
  { id: 'm12', label: 'M12 (December)', month: 'December', number: 12 },
] as const;

export const QUARTERS = [
  {
    id: 'Q1',
    label: 'Q1 (Jan-Mar)',
    months: 'Jan, Feb, Mar',
    monthNumbers: [1, 2, 3],
  },
  {
    id: 'Q2',
    label: 'Q2 (Feb-Apr)',
    months: 'Feb, Mar, Apr',
    monthNumbers: [2, 3, 4],
  },
  {
    id: 'Q3',
    label: 'Q3 (Mar-May)',
    months: 'Mar, Apr, May',
    monthNumbers: [3, 4, 5],
  },
  {
    id: 'Q4',
    label: 'Q4 (Apr-Jun)',
    months: 'Apr, May, Jun',
    monthNumbers: [4, 5, 6],
  },
  {
    id: 'Q5',
    label: 'Q5 (May-Jul)',
    months: 'May, Jun, Jul',
    monthNumbers: [5, 6, 7],
  },
  {
    id: 'Q6',
    label: 'Q6 (Jun-Aug)',
    months: 'Jun, Jul, Aug',
    monthNumbers: [6, 7, 8],
  },
  {
    id: 'Q7',
    label: 'Q7 (Jul-Sep)',
    months: 'Jul, Aug, Sep',
    monthNumbers: [7, 8, 9],
  },
  {
    id: 'Q8',
    label: 'Q8 (Aug-Oct)',
    months: 'Aug, Sep, Oct',
    monthNumbers: [8, 9, 10],
  },
  {
    id: 'Q9',
    label: 'Q9 (Sep-Nov)',
    months: 'Sep, Oct, Nov',
    monthNumbers: [9, 10, 11],
  },
  {
    id: 'Q10',
    label: 'Q10 (Oct-Dec)',
    months: 'Oct, Nov, Dec',
    monthNumbers: [10, 11, 12],
  },
  {
    id: 'Q11',
    label: 'Q11 (Nov-Jan)',
    months: 'Nov, Dec, Jan',
    monthNumbers: [11, 12, 1],
  },
  {
    id: 'Q12',
    label: 'Q12 (Dec-Feb)',
    months: 'Dec, Jan, Feb',
    monthNumbers: [12, 1, 2],
  },
] as const;

// Quarter succession mapping for rolling quarterly investments
export const QUARTER_SUCCESSORS: Record<string, string> = {
  // Main quarterly sequence (3-month gaps)
  q1: 'q4', // Jan-Mar → Apr-Jun
  q4: 'q7', // Apr-Jun → Jul-Sep
  q7: 'q10', // Jul-Sep → Oct-Dec
  q10: 'q1', // Oct-Dec → Jan-Mar (next year)

  // Overlapping sequence 1 (starting Feb)
  q2: 'q5', // Feb-Apr → May-Jul
  q5: 'q8', // May-Jul → Aug-Oct
  q8: 'q11', // Aug-Oct → Nov-Jan
  q11: 'q2', // Nov-Jan → Feb-Apr (next year)

  // Overlapping sequence 2 (starting Mar)
  q3: 'q6', // Mar-May → Jun-Aug
  q6: 'q9', // Jun-Aug → Sep-Nov
  q9: 'q12', // Sep-Nov → Dec-Feb
  q12: 'q3', // Dec-Feb → Mar-May (next year)
};

// Reverse mapping to find predecessor quarters
export const QUARTER_PREDECESSORS: Record<string, string> = Object.fromEntries(
  Object.entries(QUARTER_SUCCESSORS).map(([key, value]) => [value, key])
);

export const INVESTMENT_STATUS = {
  NEW_MONTH_1: 'new_month_1',
  NEW_MONTH_2: 'new_month_2',
  QUARTERLY_ONLY: 'quarterly_only',
} as const;

export const INVESTMENT_STATUS_LABELS = {
  [INVESTMENT_STATUS.NEW_MONTH_1]: 'New Month 1',
  [INVESTMENT_STATUS.NEW_MONTH_2]: 'New Month 2',
  [INVESTMENT_STATUS.QUARTERLY_ONLY]: 'Quarterly Only',
} as const;

export const TRANSACTION_TYPES = {
  PNL_MONTHLY: 'pnl_monthly',
  PNL_QUARTERLY: 'pnl_quarterly',
  INVESTMENT: 'investment',
  WITHDRAWAL: 'withdrawal',
  PROFIT_DISTRIBUTION: 'profit_distribution',
  PURIFICATION: 'purification',
} as const;

export const PERIOD_TYPES = {
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
} as const;

// Helper functions
export const getMonthByNumber = (monthNumber: number) => {
  return MONTHS.find((month) => month.number === monthNumber);
};

export const getQuarterByMonths = (monthNumbers: number[]) => {
  return QUARTERS.find(
    (quarter) =>
      (quarter.monthNumbers as readonly number[]).every((num) =>
        monthNumbers.includes(num)
      ) &&
      monthNumbers.every((num) =>
        (quarter.monthNumbers as readonly number[]).includes(num)
      )
  );
};

export const getUsersForMonthlyDistribution = () => {
  // For monthly distribution, include only users in month_1 and month_2 status
  return [INVESTMENT_STATUS.NEW_MONTH_1, INVESTMENT_STATUS.NEW_MONTH_2];
};

export const getUsersForQuarterlyDistribution = () => {
  // For quarterly distribution, include only quarterly_only users
  return [INVESTMENT_STATUS.QUARTERLY_ONLY];
};

// Get current quarter based on current month
export const getCurrentQuarter = () => {
  const currentMonth = new Date().getMonth() + 1; // getMonth() returns 0-11, we need 1-12

  // Find the quarter that includes the current month
  const currentQuarter = QUARTERS.find((quarter) =>
    (quarter.monthNumbers as readonly number[]).includes(currentMonth)
  );

  return currentQuarter?.id || 'q1'; // fallback to q1 if not found
};

// Get quarter name by month number
export const getQuarterByMonth = (monthNumber: number) => {
  const quarter = QUARTERS.find((quarter) =>
    (quarter.monthNumbers as readonly number[]).includes(monthNumber)
  );
  return quarter?.id || 'q1';
};

// Utility functions for period mapping
export const getMonthPeriodId = (monthNumber: number): string => {
  return `m${monthNumber}`;
};

export const getQuarterPeriodId = (monthNumber: number): string => {
  const quarter = QUARTERS.find((q) =>
    (q.monthNumbers as readonly number[]).includes(monthNumber)
  );
  return quarter?.id || 'q1';
};

// Get the next quarter in the rolling sequence
export const getNextQuarter = (currentQuarter: string): string => {
  return QUARTER_SUCCESSORS[currentQuarter] || currentQuarter;
};

// Get the previous quarter in the rolling sequence
export const getPreviousQuarter = (currentQuarter: string): string => {
  return QUARTER_PREDECESSORS[currentQuarter] || currentQuarter;
};

// Check if a quarter is in the same succession chain as another
export const isInSameQuarterChain = (
  quarter1: string,
  quarter2: string
): boolean => {
  // Check if quarter2 can be reached from quarter1 by following successors
  let current = quarter1;
  for (let i = 0; i < 4; i++) {
    // Max 4 quarters in a chain
    if (current === quarter2) return true;
    current = QUARTER_SUCCESSORS[current];
    if (current === quarter1) break; // Completed the cycle
  }
  return false;
};

// Get all quarters in the same succession chain
export const getQuarterChain = (startQuarter: string): string[] => {
  const chain: string[] = [];
  let current = startQuarter;

  do {
    chain.push(current);
    current = QUARTER_SUCCESSORS[current];
  } while (current !== startQuarter && chain.length < 4);

  return chain;
};

// Calculate the number of quarters between two quarters in the same chain
export const getQuarterDistance = (
  fromQuarter: string,
  toQuarter: string
): number => {
  if (fromQuarter === toQuarter) return 0;

  let current = fromQuarter;
  let distance = 0;

  for (let i = 0; i < 4; i++) {
    current = QUARTER_SUCCESSORS[current];
    distance++;
    if (current === toQuarter) return distance;
    if (current === fromQuarter) break; // Completed the cycle
  }

  return -1; // Not in the same chain
};

// Helper functions for quarterly capital management
export const quarterlyCapitalHelpers = {
  // Calculate opening balance for a new quarterly position
  getOpeningBalanceForNewQuarter: (
    previousQuarterClosingBalance: number,
    isFirstQuarterInChain: boolean = false
  ): number => {
    if (isFirstQuarterInChain) {
      // For the first quarter in a chain, use the user's quarterly_capital
      return previousQuarterClosingBalance;
    }
    // For subsequent quarters, use the previous quarter's closing balance
    return previousQuarterClosingBalance;
  },

  // Calculate profit allocation for quarterly positions
  calculateQuarterlyProfitAllocation: (
    principalAmount: number,
    profitRate: number
  ): number => {
    return principalAmount * profitRate;
  },

  // Update quarterly position with new profit
  updateQuarterlyBalance: (
    principalAmount: number,
    accumulatedProfit: number,
    newProfit: number
  ): { accumulated_profit: number; current_balance: number } => {
    const newAccumulatedProfit = accumulatedProfit + newProfit;
    const newCurrentBalance = principalAmount + newAccumulatedProfit;

    return {
      accumulated_profit: newAccumulatedProfit,
      current_balance: newCurrentBalance,
    };
  },

  // Determine if it's time to transition to the next quarter
  shouldTransitionQuarter: (
    currentQuarter: string,
    currentDate: Date = new Date()
  ): boolean => {
    const currentMonth = currentDate.getMonth() + 1;
    const currentQuarterInfo = QUARTERS.find((q) => q.id === currentQuarter);

    if (!currentQuarterInfo) return false;

    // Check if current month is no longer in the current quarter's range
    return !(currentQuarterInfo.monthNumbers as readonly number[]).includes(
      currentMonth
    );
  },

  // Get the next quarter for transition
  getNextQuarterForTransition: (currentQuarter: string): string => {
    return QUARTER_SUCCESSORS[currentQuarter] || currentQuarter;
  },
};

// Get opening balance for a quarter based on predecessor's closing balance
export const getOpeningBalanceForQuarter = (
  currentQuarter: string,
  closingBalances: Record<string, number>
): number => {
  const previousQuarter = getPreviousQuarter(currentQuarter);
  return previousQuarter !== currentQuarter
    ? closingBalances[previousQuarter] || 0
    : 0;
};

// High precision currency formatting for fintech
export const formatCurrency = (
  amount: number,
  decimals: number = 3
): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);
};

export const formatNumber = (amount: number, decimals: number = 3): string => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);
};

// Calculate capital split for different investment statuses
export const calculateCapitalSplit = (
  amount: number,
  investmentStatus: string
): { monthly: number; quarterly: number } => {
  switch (investmentStatus) {
    case 'new_month_1':
      return {
        monthly: Math.round(((amount * 2) / 3) * 1000000) / 1000000, // 2/3 with 6 decimal precision
        quarterly: Math.round(((amount * 1) / 3) * 1000000) / 1000000, // 1/3 with 6 decimal precision
      };
    case 'new_month_2':
      return {
        monthly: Math.round((amount / 2) * 1000000) / 1000000, // 1/2 with 6 decimal precision
        quarterly: Math.round((amount / 2) * 1000000) / 1000000, // 1/2 with 6 decimal precision
      };
    case 'quarterly_only':
      return {
        monthly: 0,
        quarterly: Math.round(amount * 1000000) / 1000000, // All to quarterly with 6 decimal precision
      };
    default:
      return { monthly: 0, quarterly: 0 };
  }
};
