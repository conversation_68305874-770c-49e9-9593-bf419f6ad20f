
import { useEffect, useState } from 'react';
import { Device, DeviceInfo } from '@capacitor/device';

export const useDevice = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);

  useEffect(() => {
    const getDeviceInfo = async () => {
      try {
        const info = await Device.getInfo();
        setDeviceInfo(info);
      } catch (error) {
        console.log('Device info not available:', error);
      }
    };

    getDeviceInfo();
  }, []);

  return deviceInfo;
};
