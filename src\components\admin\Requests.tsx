/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Check,
  X,
  Loader2,
  ArrowUpCircle,
  ArrowDownCircle,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
  useOrganizationInvestmentRequests,
  useUpdateInvestmentRequestStatus,
  useOrganizationWithdrawalRequests,
  useUpdateWithdrawalRequestStatus,
} from '@/hooks/useApi';

const Requests: React.FC = () => {
  const { user } = useAuth();

  // Investment requests
  const {
    data: investmentRequestsData,
    isLoading: investmentLoading,
    error: investmentError,
  } = useOrganizationInvestmentRequests(user?.organization_id || '');

  // Withdrawal requests
  const {
    data: withdrawalRequestsData,
    isLoading: withdrawalLoading,
    error: withdrawalError,
  } = useOrganizationWithdrawalRequests(user?.organization_id || '');

  const updateInvestmentRequestMutation = useUpdateInvestmentRequestStatus();
  const updateWithdrawalRequestMutation = useUpdateWithdrawalRequestStatus();

  const handleInvestmentRequestAction = (
    id: string,
    action: 'approved' | 'rejected',
    adminNotes?: string
  ) => {
    if (!user) return;

    updateInvestmentRequestMutation.mutate({
      requestId: id,
      status: action,
      adminId: user.id,
      adminNotes,
    });
  };

  const handleWithdrawalRequestAction = (
    id: string,
    action: 'approved' | 'rejected',
    adminNotes?: string
  ) => {
    if (!user) return;

    updateWithdrawalRequestMutation.mutate({
      requestId: id,
      status: action,
      adminId: user.id,
      adminNotes,
    });
  };

  // Filter only pending requests
  const pendingInvestmentRequests =
    investmentRequestsData?.data?.filter((r: any) => r.status === 'pending') ||
    [];
  const pendingWithdrawalRequests =
    withdrawalRequestsData?.data?.filter((r: any) => r.status === 'pending') ||
    [];

  const isLoading = investmentLoading || withdrawalLoading;
  const error = investmentError || withdrawalError;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Pending Requests</h2>
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading requests...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Pending Requests</h2>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              Error loading requests. Please try again.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Pending Requests</h2>

      <Tabs defaultValue="investment" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="investment" className="flex items-center gap-2">
            <ArrowUpCircle className="h-4 w-4" />
            Investment Requests ({pendingInvestmentRequests.length})
          </TabsTrigger>
          <TabsTrigger value="withdrawal" className="flex items-center gap-2">
            <ArrowDownCircle className="h-4 w-4" />
            Withdrawal Requests ({pendingWithdrawalRequests.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="investment" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Client Name</TableHead>
                    <TableHead>Client ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>User Status</TableHead>
                    <TableHead>Capital Split</TableHead>
                    <TableHead>Payment Method</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingInvestmentRequests.map((request: any) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {request.client?.first_name}{' '}
                            {request.client?.last_name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {request.client?.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="font-mono text-sm">{request.client_id}</p>
                      </TableCell>
                      <TableCell className="font-semibold text-blue-600">
                        ₹{request.amount?.toLocaleString() || 0}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {request.user_status_when_requested || 'new_month_1'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          {(() => {
                            const amount = request.amount || 0;
                            const status =
                              request.user_status_when_requested ||
                              'new_month_1';
                            let monthly = 0,
                              quarterly = 0;

                            if (status === 'new_month_1') {
                              monthly =
                                Math.round(((amount * 2) / 3) * 100) / 100;
                              quarterly = Math.round((amount / 3) * 100) / 100;
                            } else if (status === 'new_month_2') {
                              monthly = Math.round((amount / 2) * 100) / 100;
                              quarterly = Math.round((amount / 2) * 100) / 100;
                            } else if (status === 'quarterly_only') {
                              monthly = 0;
                              quarterly = amount;
                            }

                            return (
                              <>
                                <div className="text-green-600">
                                  Monthly: ₹{monthly.toLocaleString()}
                                </div>
                                <div className="text-purple-600">
                                  Quarterly: ₹{quarterly.toLocaleString()}
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {request.payment_method === 'upi'
                            ? 'UPI'
                            : 'Bank Transfer'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {request.created_at
                          ? new Date(request.created_at).toLocaleDateString()
                          : 'N/A'}
                      </TableCell>
                      <TableCell className="space-x-2">
                        <Button
                          size="sm"
                          onClick={() =>
                            handleInvestmentRequestAction(
                              request.id,
                              'approved'
                            )
                          }
                          disabled={updateInvestmentRequestMutation.isPending}
                        >
                          <Check className="w-4 h-4 mr-1" /> Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() =>
                            handleInvestmentRequestAction(
                              request.id,
                              'rejected'
                            )
                          }
                          disabled={updateInvestmentRequestMutation.isPending}
                        >
                          <X className="w-4 h-4 mr-1" /> Reject
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {pendingInvestmentRequests.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No pending investment requests
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="withdrawal" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Client Name</TableHead>
                    <TableHead>Client ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>User Status</TableHead>
                    <TableHead>Capital Deduction</TableHead>
                    <TableHead>Withdrawal Method</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingWithdrawalRequests.map((request: any) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">
                            {request.client?.first_name}{' '}
                            {request.client?.last_name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {request.client?.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="font-mono text-sm">{request.client_id}</p>
                      </TableCell>
                      <TableCell className="font-semibold text-red-600">
                        ₹{request.amount?.toLocaleString() || 0}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {request.user_status_when_requested || 'new_month_2'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm space-y-1">
                          {(() => {
                            const amount = request.amount || 0;
                            const status =
                              request.user_status_when_requested ||
                              'new_month_2';
                            let monthlyDeduction = 0,
                              quarterlyDeduction = 0;

                            if (status === 'new_month_2') {
                              monthlyDeduction = amount;
                              quarterlyDeduction = 0;
                            } else if (status === 'quarterly_only') {
                              monthlyDeduction = 0;
                              quarterlyDeduction = amount;
                            }
                            // new_month_1 users cannot withdraw

                            return (
                              <>
                                <div className="text-red-600">
                                  Monthly: -₹{monthlyDeduction.toLocaleString()}
                                </div>
                                <div className="text-red-600">
                                  Quarterly: -₹
                                  {quarterlyDeduction.toLocaleString()}
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {request.withdrawal_method === 'upi'
                            ? 'UPI'
                            : 'Bank Transfer'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {request.created_at
                          ? new Date(request.created_at).toLocaleDateString()
                          : 'N/A'}
                      </TableCell>
                      <TableCell className="space-x-2">
                        <Button
                          size="sm"
                          onClick={() =>
                            handleWithdrawalRequestAction(
                              request.id,
                              'approved'
                            )
                          }
                          disabled={updateWithdrawalRequestMutation.isPending}
                        >
                          <Check className="w-4 h-4 mr-1" /> Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() =>
                            handleWithdrawalRequestAction(
                              request.id,
                              'rejected'
                            )
                          }
                          disabled={updateWithdrawalRequestMutation.isPending}
                        >
                          <X className="w-4 h-4 mr-1" /> Reject
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                  {pendingWithdrawalRequests.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No pending withdrawal requests
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Requests;
