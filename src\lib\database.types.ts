export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string;
          name: string;
          total_investments: number;
          total_capital_quarterly: number;
          total_capital_monthly: number;
          total_client_profit: number;
          total_maven_profit: number;
          total_purification: number;
          total_users: number;
          total_cycles_completed: number;
          default_purification_rate: number;
          default_client_profit_rate: number;
          default_maven_profit_rate: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          total_investments?: number;
          total_capital_quarterly?: number;
          total_capital_monthly?: number;
          total_client_profit?: number;
          total_maven_profit?: number;
          total_purification?: number;
          total_users?: number;
          total_cycles_completed?: number;
          default_purification_rate?: number;
          default_client_profit_rate?: number;
          default_maven_profit_rate?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          total_investments?: number;
          total_capital_quarterly?: number;
          total_capital_monthly?: number;
          total_client_profit?: number;
          total_maven_profit?: number;
          total_purification?: number;
          total_users?: number;
          total_cycles_completed?: number;
          default_purification_rate?: number;
          default_client_profit_rate?: number;
          default_maven_profit_rate?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      users: {
        Row: {
          id: string;
          organization_id: string;
          email: string;
          password_hash: string;
          role: 'admin' | 'client';
          full_name: string;
          user_id: string;
          is_active: boolean;
          last_login: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          email: string;
          password_hash: string;
          role: 'admin' | 'client';
          full_name: string;
          user_id: string;
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          email?: string;
          password_hash?: string;
          role?: 'admin' | 'client';
          full_name?: string;
          user_id?: string;
          is_active?: boolean;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      client_accounts: {
        Row: {
          id: string;
          user_id: string;
          organization_id: string;
          current_capital: number;
          total_invested: number;
          total_withdrawn: number;
          total_profit_received: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          organization_id: string;
          current_capital?: number;
          total_invested?: number;
          total_withdrawn?: number;
          total_profit_received?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          organization_id?: string;
          current_capital?: number;
          total_invested?: number;
          total_withdrawn?: number;
          total_profit_received?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          organization_id: string;
          user_id: string | null;
          transaction_type:
            | 'investment'
            | 'withdrawal'
            | 'pnl_entry'
            | 'profit_distribution'
            | 'purification';
          amount: number;
          quarter: string | null;
          month: string | null;
          period_type: 'quarterly' | 'monthly' | null;
          description: string | null;
          reference_id: string | null;
          status: 'pending' | 'approved' | 'rejected';
          processed_by: string | null;
          processed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          user_id?: string | null;
          transaction_type:
            | 'investment'
            | 'withdrawal'
            | 'pnl_entry'
            | 'profit_distribution'
            | 'purification';
          amount: number;
          quarter?: string | null;
          month?: string | null;
          period_type?: 'quarterly' | 'monthly' | null;
          description?: string | null;
          reference_id?: string | null;
          status?: 'pending' | 'approved' | 'rejected';
          processed_by?: string | null;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          user_id?: string | null;
          transaction_type?:
            | 'investment'
            | 'withdrawal'
            | 'pnl_entry'
            | 'profit_distribution'
            | 'purification';
          amount?: number;
          quarter?: string | null;
          month?: string | null;
          period_type?: 'quarterly' | 'monthly' | null;
          description?: string | null;
          reference_id?: string | null;
          status?: 'pending' | 'approved' | 'rejected';
          processed_by?: string | null;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      requests: {
        Row: {
          id: string;
          organization_id: string;
          client_id: string;
          request_type: 'investment' | 'withdrawal';
          amount: number;
          quarter: string | null;
          status: 'pending' | 'approved' | 'rejected';
          description: string | null;
          admin_notes: string | null;
          processed_by: string | null;
          processed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          client_id: string;
          request_type: 'investment' | 'withdrawal';
          amount: number;
          quarter?: string | null;
          status?: 'pending' | 'approved' | 'rejected';
          description?: string | null;
          admin_notes?: string | null;
          processed_by?: string | null;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          client_id?: string;
          request_type?: 'investment' | 'withdrawal';
          amount?: number;
          quarter?: string | null;
          status?: 'pending' | 'approved' | 'rejected';
          description?: string | null;
          admin_notes?: string | null;
          processed_by?: string | null;
          processed_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      client_balance_sheets: {
        Row: {
          id: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id: string;
          period_type: string;
          period_month: number;
          period_year: number;
          opening_balance: number;
          investments_made: number;
          pnl_earned: number;
          withdrawals_made: number;
          closing_balance: number;
          monthly_capital_used: number;
          quarterly_capital_used: number;
          profit_rate_applied: number;
          user_status_at_creation: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id: string;
          period_type: string;
          period_month: number;
          period_year: number;
          opening_balance?: number;
          investments_made?: number;
          pnl_earned?: number;
          withdrawals_made?: number;
          closing_balance?: number;
          monthly_capital_used?: number;
          quarterly_capital_used?: number;
          profit_rate_applied?: number;
          user_status_at_creation: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          client_id?: string;
          organization_id?: string;
          admin_balance_sheet_id?: string;
          period_type?: string;
          period_month?: number;
          period_year?: number;
          opening_balance?: number;
          investments_made?: number;
          pnl_earned?: number;
          withdrawals_made?: number;
          closing_balance?: number;
          monthly_capital_used?: number;
          quarterly_capital_used?: number;
          profit_rate_applied?: number;
          user_status_at_creation?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      admin_balance_sheets: {
        Row: {
          id: string;
          organization_id: string;
          period_type: string;
          period_month: number;
          period_year: number;
          quarter_name: string | null;
          total_profit_input: number;
          purification_rate: number;
          client_profit_rate: number;
          maven_profit_rate: number;
          purification_amount: number;
          client_profit_amount: number;
          maven_profit_amount: number;
          effective_profit: number;
          total_capital_base: number;
          profit_percentage: number;
          transaction_window_duration: number;
          window_start_date: string;
          window_end_date: string;
          is_window_closed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          organization_id: string;
          period_type: string;
          period_month: number;
          period_year: number;
          quarter_name?: string | null;
          total_profit_input: number;
          purification_rate?: number;
          client_profit_rate?: number;
          maven_profit_rate?: number;
          purification_amount: number;
          client_profit_amount: number;
          maven_profit_amount: number;
          effective_profit: number;
          total_capital_base: number;
          profit_percentage: number;
          transaction_window_duration?: number;
          window_start_date: string;
          window_end_date: string;
          is_window_closed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          organization_id?: string;
          period_type?: string;
          period_month?: number;
          period_year?: number;
          quarter_name?: string | null;
          total_profit_input?: number;
          purification_rate?: number;
          client_profit_rate?: number;
          maven_profit_rate?: number;
          purification_amount?: number;
          client_profit_amount?: number;
          maven_profit_amount?: number;
          effective_profit?: number;
          total_capital_base?: number;
          profit_percentage?: number;
          transaction_window_duration?: number;
          window_start_date?: string;
          window_end_date?: string;
          is_window_closed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          organization_id: string | null;
          email: string;
          first_name: string;
          last_name: string;
          phone: string | null;
          role: string;
          upi_id: string | null;
          bank_name: string | null;
          bank_ifsc: string | null;
          bank_account_number: string | null;
          bank_account_holder: string | null;
          is_active: boolean | null;
          investment_status: string | null;
          split_cycle_start_date: string | null;
          split_cycle_completed_date: string | null;
          created_at: string | null;
          updated_at: string | null;
          total_investment: number | null;
          total_profit: number | null;
          withdrawal_funds: number | null;
          monthly_capital: number | null;
          quarterly_capital: number | null;
          completed_cycles: number | null;
        };
        Insert: {
          id: string;
          organization_id?: string | null;
          email: string;
          first_name: string;
          last_name: string;
          phone?: string | null;
          role: string;
          upi_id?: string | null;
          bank_name?: string | null;
          bank_ifsc?: string | null;
          bank_account_number?: string | null;
          bank_account_holder?: string | null;
          is_active?: boolean | null;
          investment_status?: string | null;
          split_cycle_start_date?: string | null;
          split_cycle_completed_date?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          total_investment?: number | null;
          total_profit?: number | null;
          withdrawal_funds?: number | null;
          monthly_capital?: number | null;
          quarterly_capital?: number | null;
          completed_cycles?: number | null;
        };
        Update: {
          id?: string;
          organization_id?: string | null;
          email?: string;
          first_name?: string;
          last_name?: string;
          phone?: string | null;
          role?: string;
          upi_id?: string | null;
          bank_name?: string | null;
          bank_ifsc?: string | null;
          bank_account_number?: string | null;
          bank_account_holder?: string | null;
          is_active?: boolean | null;
          investment_status?: string | null;
          split_cycle_start_date?: string | null;
          split_cycle_completed_date?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
          total_investment?: number | null;
          total_profit?: number | null;
          withdrawal_funds?: number | null;
          monthly_capital?: number | null;
          quarterly_capital?: number | null;
          completed_cycles?: number | null;
        };
      };
      investment_requests: {
        Row: {
          id: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id: string | null;
          amount: number;
          payment_method: string;
          status: string;
          admin_notes: string | null;
          approved_by: string | null;
          approved_at: string | null;
          rejection_reason: string | null;
          monthly_capital_added: number | null;
          quarterly_capital_added: number | null;
          user_status_when_approved: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id?: string | null;
          amount: number;
          payment_method: string;
          status?: string;
          admin_notes?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
          monthly_capital_added?: number | null;
          quarterly_capital_added?: number | null;
          user_status_when_approved?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          client_id?: string;
          organization_id?: string;
          admin_balance_sheet_id?: string | null;
          amount?: number;
          payment_method?: string;
          status?: string;
          admin_notes?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
          monthly_capital_added?: number | null;
          quarterly_capital_added?: number | null;
          user_status_when_approved?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      withdrawal_requests: {
        Row: {
          id: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id: string | null;
          amount: number;
          withdrawal_method: string;
          status: string;
          admin_notes: string | null;
          approved_by: string | null;
          approved_at: string | null;
          rejection_reason: string | null;
          user_status_when_requested: string;
          available_funds_at_request: number;
          monthly_capital_deducted: number | null;
          quarterly_capital_deducted: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          client_id: string;
          organization_id: string;
          admin_balance_sheet_id?: string | null;
          amount: number;
          withdrawal_method?: string;
          status?: string;
          admin_notes?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
          user_status_when_requested: string;
          available_funds_at_request: number;
          monthly_capital_deducted?: number | null;
          quarterly_capital_deducted?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          client_id?: string;
          organization_id?: string;
          admin_balance_sheet_id?: string | null;
          amount?: number;
          withdrawal_method?: string;
          status?: string;
          admin_notes?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
          user_status_when_requested?: string;
          available_funds_at_request?: number;
          monthly_capital_deducted?: number | null;
          quarterly_capital_deducted?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_quarterly_positions: {
        Row: {
          id: string;
          client_id: string;
          organization_id: string;
          quarter_name: string;
          principal_amount: number;
          accumulated_profit: number;
          current_balance: number;
          start_date: string;
          end_date: string | null;
          successor_quarter: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          client_id: string;
          organization_id: string;
          quarter_name: string;
          principal_amount: number;
          accumulated_profit?: number;
          current_balance: number;
          start_date: string;
          end_date?: string | null;
          successor_quarter?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          client_id?: string;
          organization_id?: string;
          quarter_name?: string;
          principal_amount?: number;
          accumulated_profit?: number;
          current_balance?: number;
          start_date?: string;
          end_date?: string | null;
          successor_quarter?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      client_overview: {
        Row: {
          user_id: string;
          organization_id: string;
          full_name: string;
          display_user_id: string;
          current_capital: number;
          total_invested: number;
          total_withdrawn: number;
          total_profit_received: number;
          total_pnl_percentage: number;
          withdrawal_limit_percentage: number;
          withdrawal_limit: number;
          pending_requests: number;
        };
      };
      admin_overview: {
        Row: {
          organization_id: string;
          organization_name: string;
          total_capital: number;
          total_clients: number;
          total_profit_distributed: number;
          withdrawal_limit_percentage: number;
          pending_requests: number;
          latest_pnl_percentage: number;
        };
      };
    };
  };
}
