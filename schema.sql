-- Maven Investments - Complete Supabase Schema
-- This schema supports multi-tenant architecture with proper security policies

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- ENUMS AND TYPES
-- =============================================

CREATE TYPE user_role AS ENUM ('admin', 'client');
CREATE TYPE transaction_type AS ENUM ('investment', 'withdrawal', 'pnl_entry', 'profit_distribution', 'purification');
CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE period_type AS ENUM ('quarterly', 'monthly');

-- =============================================
-- CORE TABLES
-- =============================================

-- Organizations/Tenants table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    maven_percentage DECIMAL(5,2) DEFAULT 28.00,
    client_percentage DECIMAL(5,2) DEFAULT 40.00,
    purification_percentage DECIMAL(5,2) DEFAULT 5.00,
    withdrawal_limit_percentage DECIMAL(5,2) DEFAULT 50.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table (both admins and clients)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    user_id VARCHAR(50) NOT NULL, -- Custom user ID for display
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);

-- Client accounts (extended info for clients)
CREATE TABLE client_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    current_capital DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(15,2) DEFAULT 0.00,
    total_profit_received DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table (all financial movements)
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    transaction_type transaction_type NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    quarter VARCHAR(10), -- Q1-2024, Q2-2024, etc.
    month VARCHAR(10), -- M1-2024, M2-2024, etc.
    period_type period_type,
    description TEXT,
    reference_id VARCHAR(100), -- External reference
    status request_status DEFAULT 'approved',
    processed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Investment/Withdrawal requests
CREATE TABLE requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    request_type transaction_type NOT NULL CHECK (request_type IN ('investment', 'withdrawal')),
    amount DECIMAL(15,2) NOT NULL,
    quarter VARCHAR(10),
    status request_status DEFAULT 'pending',
    description TEXT,
    admin_notes TEXT,
    processed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- PnL entries for quarterly/monthly calculations
CREATE TABLE pnl_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    period VARCHAR(10) NOT NULL, -- Q1-2024, M1-2024, etc.
    period_type period_type NOT NULL,
    total_capital DECIMAL(15,2) NOT NULL,
    pnl_amount DECIMAL(15,2) NOT NULL,
    pnl_percentage DECIMAL(8,4) NOT NULL,
    is_distributed BOOLEAN DEFAULT false,
    distributed_at TIMESTAMP WITH TIME ZONE,
    distributed_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(organization_id, period, period_type)
);

-- Profit distribution records
CREATE TABLE profit_distributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    pnl_entry_id UUID NOT NULL REFERENCES pnl_entries(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    client_capital DECIMAL(15,2) NOT NULL,
    client_profit DECIMAL(15,2) NOT NULL,
    maven_profit DECIMAL(15,2) NOT NULL,
    purification_amount DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit log for tracking all important actions
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users table indexes
CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- Transactions table indexes
CREATE INDEX idx_transactions_organization_id ON transactions(organization_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_quarter ON transactions(quarter);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);

-- Requests table indexes
CREATE INDEX idx_requests_organization_id ON requests(organization_id);
CREATE INDEX idx_requests_client_id ON requests(client_id);
CREATE INDEX idx_requests_status ON requests(status);
CREATE INDEX idx_requests_created_at ON requests(created_at);

-- Client accounts indexes
CREATE INDEX idx_client_accounts_organization_id ON client_accounts(organization_id);
CREATE INDEX idx_client_accounts_user_id ON client_accounts(user_id);

-- PnL entries indexes
CREATE INDEX idx_pnl_entries_organization_id ON pnl_entries(organization_id);
CREATE INDEX idx_pnl_entries_period ON pnl_entries(period);

-- Profit distributions indexes
CREATE INDEX idx_profit_distributions_organization_id ON profit_distributions(organization_id);
CREATE INDEX idx_profit_distributions_client_id ON profit_distributions(client_id);
CREATE INDEX idx_profit_distributions_pnl_entry_id ON profit_distributions(pnl_entry_id);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Temporarily disable RLS for custom authentication
-- TODO: Re-enable with proper policies once custom auth is working
-- ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE client_accounts ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE requests ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE pnl_entries ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE profit_distributions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Organizations policies (disabled for custom auth)
-- CREATE POLICY "Users can view their organization" ON organizations
--     FOR SELECT USING (
--         id IN (
--             SELECT organization_id FROM users
--             WHERE id = auth.uid()
--         )
--     );

-- Users policies (disabled for custom auth)
-- CREATE POLICY "Users can view users in their organization" ON users
--     FOR SELECT USING (
--         organization_id IN (
--             SELECT organization_id FROM users
--             WHERE id = auth.uid()
--         )
--     );

-- CREATE POLICY "Admins can insert users in their organization" ON users
--     FOR INSERT WITH CHECK (
--         organization_id IN (
--             SELECT organization_id FROM users
--             WHERE id = auth.uid() AND role = 'admin'
--         )
--     );

-- CREATE POLICY "Admins can update users in their organization" ON users
--     FOR UPDATE USING (
--         organization_id IN (
--             SELECT organization_id FROM users
--             WHERE id = auth.uid() AND role = 'admin'
--         )
--     );

-- Client accounts policies
CREATE POLICY "Users can view client accounts in their organization" ON client_accounts
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Clients can only view their own account" ON client_accounts
    FOR SELECT USING (
        user_id = auth.uid() OR 
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Transactions policies
CREATE POLICY "Users can view transactions in their organization" ON transactions
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        ) AND (
            -- Admins can see all transactions
            EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() AND role = 'admin'
            ) OR
            -- Clients can only see their own transactions
            user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can insert transactions" ON transactions
    FOR INSERT WITH CHECK (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Requests policies
CREATE POLICY "Users can view relevant requests" ON requests
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        ) AND (
            -- Admins can see all requests
            EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() AND role = 'admin'
            ) OR
            -- Clients can only see their own requests
            client_id = auth.uid()
        )
    );

CREATE POLICY "Clients can create requests" ON requests
    FOR INSERT WITH CHECK (
        client_id = auth.uid() AND
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Admins can update requests" ON requests
    FOR UPDATE USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- PnL entries policies
CREATE POLICY "Users can view PnL entries in their organization" ON pnl_entries
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage PnL entries" ON pnl_entries
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Profit distributions policies
CREATE POLICY "Users can view relevant profit distributions" ON profit_distributions
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid()
        ) AND (
            -- Admins can see all distributions
            EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() AND role = 'admin'
            ) OR
            -- Clients can only see their own distributions
            client_id = auth.uid()
        )
    );

-- Audit logs policies (Admin only)
CREATE POLICY "Admins can view audit logs" ON audit_logs
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_accounts_updated_at BEFORE UPDATE ON client_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_requests_updated_at BEFORE UPDATE ON requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pnl_entries_updated_at BEFORE UPDATE ON pnl_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create client account when user is created
CREATE OR REPLACE FUNCTION create_client_account()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.role = 'client' THEN
        INSERT INTO client_accounts (user_id, organization_id)
        VALUES (NEW.id, NEW.organization_id);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER create_client_account_trigger
    AFTER INSERT ON users
    FOR EACH ROW EXECUTE FUNCTION create_client_account();

-- Function to log audit trail
CREATE OR REPLACE FUNCTION log_audit_trail()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (
            organization_id, user_id, action, table_name, record_id, new_values
        ) VALUES (
            NEW.organization_id, auth.uid(), TG_OP, TG_TABLE_NAME, NEW.id, row_to_json(NEW)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (
            organization_id, user_id, action, table_name, record_id, old_values, new_values
        ) VALUES (
            NEW.organization_id, auth.uid(), TG_OP, TG_TABLE_NAME, NEW.id, row_to_json(OLD), row_to_json(NEW)
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (
            organization_id, user_id, action, table_name, record_id, old_values
        ) VALUES (
            OLD.organization_id, auth.uid(), TG_OP, TG_TABLE_NAME, OLD.id, row_to_json(OLD)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Add audit triggers for sensitive tables
CREATE TRIGGER audit_users_trigger
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

CREATE TRIGGER audit_transactions_trigger
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

CREATE TRIGGER audit_requests_trigger
    AFTER INSERT OR UPDATE OR DELETE ON requests
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

-- View for client dashboard overview
CREATE VIEW client_overview AS
SELECT 
    u.id as user_id,
    u.organization_id,
    u.full_name,
    u.user_id as display_user_id,
    ca.current_capital,
    ca.total_invested,
    ca.total_withdrawn,
    ca.total_profit_received,
    COALESCE(
        (ca.total_profit_received / NULLIF(ca.total_invested, 0)) * 100, 0
    ) as total_pnl_percentage,
    org.withdrawal_limit_percentage,
    (ca.current_capital * org.withdrawal_limit_percentage / 100) as withdrawal_limit,
    (
        SELECT COUNT(*) FROM requests r 
        WHERE r.client_id = u.id AND r.status = 'pending'
    ) as pending_requests
FROM users u
JOIN client_accounts ca ON u.id = ca.user_id
JOIN organizations org ON u.organization_id = org.id
WHERE u.role = 'client' AND u.is_active = true;

-- View for admin dashboard overview
CREATE VIEW admin_overview AS
SELECT 
    org.id as organization_id,
    org.name as organization_name,
    SUM(ca.current_capital) as total_capital,
    COUNT(DISTINCT u.id) as total_clients,
    SUM(ca.total_profit_received) as total_profit_distributed,
    org.withdrawal_limit_percentage,
    (
        SELECT COUNT(*) FROM requests r 
        WHERE r.organization_id = org.id AND r.status = 'pending'
    ) as pending_requests,
    -- Calculate total PnL from recent quarter
    COALESCE(
        (SELECT pnl_percentage FROM pnl_entries pe 
         WHERE pe.organization_id = org.id 
         ORDER BY pe.created_at DESC LIMIT 1), 0
    ) as latest_pnl_percentage
FROM organizations org
LEFT JOIN users u ON org.id = u.organization_id AND u.role = 'client' AND u.is_active = true
LEFT JOIN client_accounts ca ON u.id = ca.user_id
GROUP BY org.id, org.name, org.withdrawal_limit_percentage;

-- =============================================
-- SAMPLE DATA (Optional - for testing)
-- =============================================

-- Insert sample organization
INSERT INTO organizations (name, slug) VALUES 
('Maven Investments Demo', 'maven-demo');

-- Insert sample admin user (password: admin123)
INSERT INTO users (organization_id, email, password_hash, role, full_name, user_id) VALUES 
((SELECT id FROM organizations WHERE slug = 'maven-demo'), 
 '<EMAIL>', 
 crypt('admin123', gen_salt('bf')), 
 'admin', 
 'System Administrator', 
 'ADMIN001');

-- Insert sample client user (password: client123)
INSERT INTO users (organization_id, email, password_hash, role, full_name, user_id) VALUES 
((SELECT id FROM organizations WHERE slug = 'maven-demo'), 
 '<EMAIL>', 
 crypt('client123', gen_salt('bf')), 
 'client', 
 'John Doe', 
 'CLIENT001');

-- The client_account will be created automatically via trigger

-- =============================================
-- ADDITIONAL SECURITY FUNCTIONS
-- =============================================

-- Function to validate password strength
CREATE OR REPLACE FUNCTION validate_password(password TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN length(password) >= 8 
        AND password ~ '[A-Z]'  -- At least one uppercase
        AND password ~ '[a-z]'  -- At least one lowercase
        AND password ~ '[0-9]'; -- At least one digit
END;
$$ language 'plpgsql';

-- Function to hash password with salt
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
    IF NOT validate_password(password) THEN
        RAISE EXCEPTION 'Password must be at least 8 characters with uppercase, lowercase, and number';
    END IF;
    RETURN crypt(password, gen_salt('bf'));
END;
$$ language 'plpgsql';

-- Function to verify password
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN crypt(password, hash) = hash;
END;
$$ language 'plpgsql';

-- Function to authenticate user
CREATE OR REPLACE FUNCTION authenticate_user(user_email TEXT, user_password TEXT)
RETURNS TABLE(
    id UUID,
    organization_id UUID,
    email VARCHAR(255),
    role user_role,
    full_name VARCHAR(255),
    user_id VARCHAR(50),
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.organization_id,
        u.email,
        u.role,
        u.full_name,
        u.user_id,
        u.is_active
    FROM users u
    WHERE u.email = user_email
    AND u.is_active = true
    AND verify_password(user_password, u.password_hash);

    -- Update last login timestamp
    UPDATE users
    SET last_login = NOW()
    WHERE email = user_email AND is_active = true;
END;
$$ language 'plpgsql';