/* eslint-disable @typescript-eslint/no-explicit-any */
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://jpqnvseejsahjlbhgmju.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.-Vj_fwy4183U_helXzOcXFGua1EdOXqhH2qH1FDBTaw';

export const supabase = createClient(supabaseUrl, supabaseKey);

// =============================================
// TYPES BASED ON NEW SCHEMA
// =============================================

export interface Organization {
  id: string;
  name: string;
  total_capital: number;
  upi_id?: string;
  bank_name?: string;
  bank_ifsc?: string;
  bank_account_number?: string;
  bank_account_holder?: string;
  qr_code_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  organization_id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'admin' | 'client';
  upi_id?: string;
  bank_name?: string;
  bank_ifsc?: string;
  bank_account_number?: string;
  bank_account_holder?: string;
  is_active: boolean;
  investment_status:
    | 'new_month_1'
    | 'new_month_2'
    | 'new_month_3'
    | 'quarterly_only';
  split_cycle_start_date?: string;
  split_cycle_completed_date?: string;
  total_investment?: number;
  total_profit?: number;
  withdrawal_funds?: number;
  created_at: string;
  updated_at: string;
}

// =============================================
// PHASE 1: AUTHENTICATION & USER MANAGEMENT
// =============================================

export const authApi = {
  // Login using Supabase Auth
  signIn: async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { data: null, error };
      }

      if (data.user) {
        // Get user profile data
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', data.user.id)
          .single();

        if (profileError) {
          return { data: null, error: profileError };
        }

        return {
          data: {
            user: data.user,
            profile,
          },
          error: null,
        };
      }

      return { data: null, error: { message: 'Authentication failed' } };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Sign out
  signOut: async () => {
    return await supabase.auth.signOut();
  },

  // Get current session
  getSession: async () => {
    return await supabase.auth.getSession();
  },

  // Get current user with profile
  getCurrentUserWithProfile: async () => {
    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      if (userError || !user) {
        return { data: null, error: userError };
      }

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        return { data: null, error: profileError };
      }

      return { data: { user, profile }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
};

export const userApi = {
  // Create client user (Admin function)
  createClient: async (clientData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    phone?: string;
    organization_id: string;
    is_existing_user?: boolean;
  }) => {
    try {
      // 1. Create user in Supabase Auth with auto-confirm
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: clientData.email,
        password: clientData.password,
      });

      if (authError || !authData.user) {
        return { data: null, error: authError };
      }

      // 2. Create user profile
      const investmentStatus = clientData.is_existing_user
        ? 'quarterly_only'
        : 'new_month_1';
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user.id,
          organization_id: clientData.organization_id,
          email: clientData.email,
          first_name: clientData.first_name,
          last_name: clientData.last_name,
          phone: clientData.phone,
          role: 'client',
          is_active: true,
          investment_status: investmentStatus,
        })
        .select()
        .single();

      if (profileError) {
        // If profile creation fails, clean up the auth user
        await supabase.auth.admin.deleteUser(authData.user.id);
        return { data: null, error: profileError };
      }

      return {
        data: { user: authData.user, profile: profileData },
        error: null,
      };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get all clients for an organization
  getClients: async (organizationId: string) => {
    return await supabase
      .from('user_profiles')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('role', 'client')
      .eq('is_active', true)
      .order('created_at', { ascending: false });
  },

  // Get clients by investment status
  getClientsByInvestmentStatus: async (
    organizationId: string,
    investmentStatuses: string[]
  ) => {
    return await supabase
      .from('user_profiles')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('role', 'client')
      .eq('is_active', true)
      .in('investment_status', investmentStatuses)
      .order('created_at', { ascending: false });
  },

  // Get user profile by ID
  getUserProfile: async (userId: string) => {
    return await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
  },

  // Update user profile
  updateUserProfile: async (userId: string, updates: Partial<UserProfile>) => {
    return await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
  },

  // Update user financial metrics
  updateUserFinancials: async (
    userId: string,
    financials: {
      total_investment?: number;
      total_profit?: number;
      withdrawal_funds?: number;
    }
  ) => {
    return await supabase
      .from('user_profiles')
      .update(financials)
      .eq('id', userId)
      .select()
      .single();
  },

  // Deactivate user (soft delete)
  deactivateUser: async (userId: string) => {
    return await supabase
      .from('user_profiles')
      .update({ is_active: false })
      .eq('id', userId);
  },
};

export const organizationApi = {
  // Get organization by ID
  getOrganization: async (organizationId: string) => {
    return await supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();
  },

  // Update organization
  updateOrganization: async (
    organizationId: string,
    updates: Partial<Organization>
  ) => {
    return await supabase
      .from('organizations')
      .update(updates)
      .eq('id', organizationId)
      .select()
      .single();
  },

  // Update organization capital (for approved investments)
  updateCapital: async (organizationId: string, amount: number) => {
    const { data: org } = await supabase
      .from('organizations')
      .select('total_capital')
      .eq('id', organizationId)
      .single();

    if (org) {
      return await supabase
        .from('organizations')
        .update({ total_capital: (org.total_capital || 0) + amount })
        .eq('id', organizationId)
        .select()
        .single();
    }
    return { data: null, error: { message: 'Organization not found' } };
  },

  // Update monthly capital (for approved monthly investments)
  updateMonthlyCapital: async (organizationId: string, amount: number) => {
    const { data: org } = await supabase
      .from('organizations')
      .select('total_capital_monthly')
      .eq('id', organizationId)
      .single();

    if (org) {
      return await supabase
        .from('organizations')
        .update({
          total_capital_monthly: (org.total_capital_monthly || 0) + amount,
        })
        .eq('id', organizationId)
        .select()
        .single();
    }
    return { data: null, error: { message: 'Organization not found' } };
  },

  // Update quarterly capital (for approved quarterly investments)
  updateQuarterlyCapital: async (organizationId: string, amount: number) => {
    const { data: org } = await supabase
      .from('organizations')
      .select('total_capital_quarterly')
      .eq('id', organizationId)
      .single();

    if (org) {
      return await supabase
        .from('organizations')
        .update({
          total_capital_quarterly: (org.total_capital_quarterly || 0) + amount,
        })
        .eq('id', organizationId)
        .select()
        .single();
    }
    return { data: null, error: { message: 'Organization not found' } };
  },
};

// =============================================
// PHASE 2: INVESTMENT REQUESTS & NOTIFICATIONS
// =============================================

export interface InvestmentRequest {
  id: string;
  client_id: string;
  organization_id: string;
  amount: number;
  payment_method: 'upi' | 'bank_transfer';
  status: 'pending' | 'approved' | 'rejected';
  admin_notes?: string;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
  // Joined data
  client?: UserProfile;
}

export interface Notification {
  id: string;
  user_id: string;
  organization_id: string;
  title: string;
  message: string;
  notification_type: string;
  is_read: boolean;
  created_at: string;
}

// =============================================
// SPLIT CYCLE LOGIC
// =============================================

export const investmentRequestApi = {
  // Create investment request (Client function)
  createRequest: async (requestData: {
    client_id: string;
    organization_id: string;
    amount: number;
    payment_method: 'upi' | 'bank_transfer';
    admin_balance_sheet_id?: string | null;
    user_status_when_requested?: string;
  }) => {
    return await supabase
      .from('investment_requests')
      .insert(requestData)
      .select()
      .single();
  },

  // Get all requests for organization (Admin function)
  getRequests: async (organizationId: string) => {
    return await supabase
      .from('investment_requests')
      .select(
        `
        *,
        client:user_profiles!client_id(*)
      `
      )
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },

  // Get requests for specific client
  getClientRequests: async (clientId: string) => {
    return await supabase
      .from('investment_requests')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });
  },

  // Update request status (Admin function) - Phase 3 Implementation
  updateRequestStatus: async (
    requestId: string,
    status: 'approved' | 'rejected',
    adminId: string,
    adminNotes?: string
  ) => {
    // Get the investment request and user profile
    const { data: investmentRequest } = await supabase
      .from('investment_requests')
      .select('client_id, amount')
      .eq('id', requestId)
      .single();

    if (!investmentRequest) {
      throw new Error('Investment request not found');
    }

    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status, total_investment')
      .eq('id', investmentRequest.client_id)
      .single();

    if (!userProfile) {
      throw new Error('User profile not found');
    }

    const updateData: any = {
      status,
      approved_by: adminId,
      approved_at: new Date().toISOString(),
      user_status_when_approved: userProfile.investment_status,
    };

    if (adminNotes) {
      updateData.admin_notes = adminNotes;
    }

    // Calculate capital splits for approved requests
    if (status === 'approved' && userProfile) {
      const { data: requestData } = await supabase
        .from('investment_requests')
        .select('amount')
        .eq('id', requestId)
        .single();

      if (requestData) {
        const amount = requestData.amount;
        let monthlyCapitalAdded = 0;
        let quarterlyCapitalAdded = 0;

        // Phase 3: Apply splitting logic based on user's investment status
        if (userProfile.investment_status === 'new_month_1') {
          // new_month_1: 2/3 monthly, 1/3 quarterly
          monthlyCapitalAdded = Math.round(((amount * 2) / 3) * 100) / 100;
          quarterlyCapitalAdded = Math.round((amount / 3) * 100) / 100;
        } else if (userProfile.investment_status === 'new_month_2') {
          // new_month_2: 1/2 monthly, 1/2 quarterly
          monthlyCapitalAdded = Math.round((amount / 2) * 100) / 100;
          quarterlyCapitalAdded = Math.round((amount / 2) * 100) / 100;
        } else if (userProfile.investment_status === 'quarterly_only') {
          // quarterly_only: 100% quarterly
          monthlyCapitalAdded = 0;
          quarterlyCapitalAdded = amount;
        }

        updateData.monthly_capital_added = monthlyCapitalAdded;
        updateData.quarterly_capital_added = quarterlyCapitalAdded;
      }
    }

    const { data: updatedRequest, error } = await supabase
      .from('investment_requests')
      .update(updateData)
      .eq('id', requestId)
      .select()
      .single();

    if (error || !updatedRequest) {
      return { data: null, error };
    }

    // Phase 3: If approved, update user's total_investment
    if (status === 'approved') {
      // Update user's total_investment immediately
      const { data: currentProfile } = await supabase
        .from('user_profiles')
        .select('total_investment, organization_id')
        .eq('id', updatedRequest.client_id)
        .single();

      if (currentProfile) {
        const newTotalInvestment =
          (currentProfile.total_investment || 0) + investmentRequest.amount;

        await supabase
          .from('user_profiles')
          .update({
            total_investment: newTotalInvestment,
          })
          .eq('id', updatedRequest.client_id);

          
        console.log(
          `✓ Investment approved for user ${updatedRequest.client_id}:`,
          {
            amount: investmentRequest.amount,
            monthly_split: updatedRequest.monthly_capital_added,
            quarterly_split: updatedRequest.quarterly_capital_added,
            new_total_investment: newTotalInvestment,
            user_status: userProfile.investment_status,
          }
        );
      }
    }

    return { data: updatedRequest, error: null };
  },

  // Get pending requests count
  getPendingRequestsCount: async (organizationId: string) => {
    const { count } = await supabase
      .from('investment_requests')
      .select('*', { count: 'exact', head: true })
      .eq('organization_id', organizationId)
      .eq('status', 'pending');

    return { data: count, error: null };
  },
};

export const withdrawalRequestApi = {
  // Create withdrawal request (Client function)
  createRequest: async (requestData: {
    client_id: string;
    organization_id: string;
    amount: number;
    withdrawal_method: 'upi' | 'bank_transfer';
    upi_id?: string;
    bank_name?: string;
    bank_ifsc?: string;
    bank_account_number?: string;
    bank_account_holder?: string;
    admin_balance_sheet_id?: string | null;
  }) => {
    // Get user profile to capture current status and withdrawal funds
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status, withdrawal_funds')
      .eq('id', requestData.client_id)
      .single();

    const insertData = {
      ...requestData,
      user_status_when_requested:
        userProfile?.investment_status || 'new_month_1',
      available_funds_at_request: userProfile?.withdrawal_funds || 0,
    };

    return await supabase
      .from('withdrawal_requests')
      .insert(insertData)
      .select()
      .single();
  },

  // Get all withdrawal requests for organization (Admin function)
  getRequests: async (organizationId: string) => {
    return await supabase
      .from('withdrawal_requests')
      .select(
        `
        *,
        client:user_profiles!client_id(*)
      `
      )
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });
  },

  // Get withdrawal requests for specific client
  getClientRequests: async (clientId: string) => {
    return await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });
  },

  // Update withdrawal request status (Admin function) - Phase 4 Implementation
  updateRequestStatus: async (
    requestId: string,
    status: 'approved' | 'rejected',
    adminId: string,
    adminNotes?: string
  ) => {
    // Get the withdrawal request and user profile
    const { data: withdrawalRequest } = await supabase
      .from('withdrawal_requests')
      .select('client_id, amount')
      .eq('id', requestId)
      .single();

    if (!withdrawalRequest) {
      throw new Error('Withdrawal request not found');
    }

    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status, total_investment')
      .eq('id', withdrawalRequest.client_id)
      .single();

    if (!userProfile) {
      throw new Error('User profile not found');
    }

    const updateData: any = {
      status,
      approved_by: adminId,
      approved_at: new Date().toISOString(),
    };

    if (adminNotes) {
      updateData.admin_notes = adminNotes;
    }

    // Phase 4: Calculate capital deductions for approved requests
    if (status === 'approved') {
      const amount = withdrawalRequest.amount;
      let monthlyCapitalDeducted = 0;
      let quarterlyCapitalDeducted = 0;

      // Calculate deduction from monthly/quarterly capitals based on user status
      if (userProfile.investment_status === 'new_month_2') {
        // For new_month_2: deduct from monthly capital
        monthlyCapitalDeducted = amount;
        quarterlyCapitalDeducted = 0;
      } else if (userProfile.investment_status === 'quarterly_only') {
        // For quarterly_only: deduct from quarterly capital
        quarterlyCapitalDeducted = amount;
        monthlyCapitalDeducted = 0;
      }
      // Note: new_month_1 users cannot withdraw (should be prevented at request creation)

      updateData.monthly_capital_deducted = monthlyCapitalDeducted;
      updateData.quarterly_capital_deducted = quarterlyCapitalDeducted;
    }

    const { data: updatedRequest, error } = await supabase
      .from('withdrawal_requests')
      .update(updateData)
      .eq('id', requestId)
      .select()
      .single();

    if (error || !updatedRequest) {
      return { data: null, error };
    }

    // Phase 4: If approved, update user's total_investment but NOT capitals (wait for window close)
    if (status === 'approved') {
      // Update user's total_investment immediately
      const { data: currentProfile } = await supabase
        .from('user_profiles')
        .select('total_investment')
        .eq('id', updatedRequest.client_id)
        .single();

      if (currentProfile) {
        const newTotalInvestment = Math.max(
          0,
          (currentProfile.total_investment || 0) - withdrawalRequest.amount
        );

        await supabase
          .from('user_profiles')
          .update({
            total_investment: newTotalInvestment,
          })
          .eq('id', updatedRequest.client_id);

        console.log(
          `✓ Withdrawal approved for user ${updatedRequest.client_id}:`,
          {
            amount: withdrawalRequest.amount,
            monthly_deduction: updatedRequest.monthly_capital_deducted,
            quarterly_deduction: updatedRequest.quarterly_capital_deducted,
            new_total_investment: newTotalInvestment,
          }
        );
      }
    }

    return { data: updatedRequest, error: null };
  },

  // Get pending withdrawal requests count
  getPendingRequestsCount: async (organizationId: string) => {
    const { count } = await supabase
      .from('withdrawal_requests')
      .select('*', { count: 'exact', head: true })
      .eq('organization_id', organizationId)
      .eq('status', 'pending');

    return { data: count, error: null };
  },
};

export const clientBalanceSheetApi = {
  // Create NEW client balance sheet for profit distribution (Admin function)
  createBalanceSheet: async (balanceSheetData: {
    client_id: string;
    organization_id: string;
    admin_balance_sheet_id: string;
    period_type: string; // 'monthly' or 'quarterly'
    period_month: number;
    period_year: number;
    opening_balance: number;
    investments_made?: number;
    pnl_earned?: number;
    withdrawals_made?: number;
    closing_balance: number;
    monthly_capital_used?: number;
    quarterly_capital_used?: number;
    profit_rate_applied: number;
    user_status_at_creation: string;
  }) => {
    return await supabase
      .from('client_balance_sheets')
      .insert({
        ...balanceSheetData,
        investments_made: 0, // Always start with 0, updated during window closure
        withdrawals_made: 0, // Always start with 0, updated during window closure
        pnl_earned: balanceSheetData.pnl_earned || 0,
        monthly_capital_used: balanceSheetData.monthly_capital_used || 0,
        quarterly_capital_used: balanceSheetData.quarterly_capital_used || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();
  },

  // Legacy upsert method for backward compatibility
  upsertBalanceSheet: async (balanceSheetData: {
    client_id: string;
    organization_id: string;
    period_month: number;
    period_year: number;
    opening_balance: number;
    investments_made?: number;
    pnl_earned?: number;
    withdrawals_made?: number;
    closing_balance: number;
  }) => {
    return await supabase
      .from('client_balance_sheets')
      .upsert(balanceSheetData, {
        onConflict: 'client_id,period_month,period_year',
      })
      .select()
      .single();
  },

  // Get the correct opening balance for a new balance sheet
  getOpeningBalance: async (
    clientId: string,
    periodType: 'monthly' | 'quarterly' = 'monthly',
    organizationId?: string,
    quarterName?: string
  ) => {
    if (periodType === 'monthly') {
      // For monthly balance sheets, use the user's current monthly_capital
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('monthly_capital')
        .eq('id', clientId)
        .single();

      return userProfile?.monthly_capital || 0;
    } else {
      // For quarterly balance sheets, use quarter_snapshots total_capital_at_end
      if (!organizationId || !quarterName) {
        throw new Error(
          'Organization ID and quarter name are required for quarterly period type'
        );
      }

      const { data: quarterSnapshot } = await supabase
        .from('quarter_snapshots')
        .select('total_capital_at_end')
        .eq('organization_id', organizationId)
        .eq('quarter_name', quarterName)
        .single();

      return quarterSnapshot?.total_capital_at_end || 0;
    }
  },

  // Get opening balance for quarterly investments using rolling quarter logic
  getQuarterlyOpeningBalance: async (
    clientId: string,
    currentQuarter: string
  ) => {
    // Import the utility function
    const { getPreviousQuarter } = await import('@/lib/constants');
    const previousQuarter = getPreviousQuarter(currentQuarter);

    if (previousQuarter === currentQuarter) {
      // This is the first quarter in the sequence, use user's quarterly capital
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('quarterly_capital')
        .eq('id', clientId)
        .single();

      return userProfile?.quarterly_capital || 0;
    }

    // Get the closing balance from the previous quarter in the rolling sequence
    const { data: previousBalanceSheet } = await supabase
      .from('client_balance_sheets')
      .select('closing_balance')
      .eq('client_id', clientId)
      .eq('period_id', previousQuarter)
      .eq('period_type', 'quarterly')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    return previousBalanceSheet?.closing_balance || 0;
  },

  // Get client balance sheets
  getClientBalanceSheets: async (clientId: string) => {
    return await supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', clientId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false });
  },

  // Get latest balance sheet for withdrawal limit calculation
  getLatestBalanceSheet: async (clientId: string) => {
    return await supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', clientId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false })
      .limit(1)
      .single();
  },

  // Get all balance sheets for organization (Admin function)
  getOrganizationBalanceSheets: async (organizationId: string) => {
    return await supabase
      .from('client_balance_sheets')
      .select(
        `
        *,
        client:user_profiles!client_id(*)
      `
      )
      .eq('organization_id', organizationId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false });
  },

  // Calculate and update user financial metrics based on balance sheets
  updateUserFinancialsFromBalanceSheets: async (clientId: string) => {
    // Get all balance sheets for the client
    const { data: balanceSheets, error } = await supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', clientId)
      .order('period_year', { ascending: true })
      .order('period_month', { ascending: true });

    if (error || !balanceSheets || balanceSheets.length === 0) {
      return {
        data: null,
        error: error || new Error('No balance sheets found'),
      };
    }

    // Calculate totals
    const totalInvestment = balanceSheets.reduce(
      (sum, sheet) => sum + (sheet.investments_made || 0),
      0
    );
    const totalProfit = balanceSheets.reduce(
      (sum, sheet) => sum + (sheet.pnl_earned || 0),
      0
    );

    // Withdrawal funds = latest closing balance
    const latestSheet = balanceSheets[balanceSheets.length - 1];
    const withdrawalFunds = latestSheet.closing_balance;

    // Update user profile
    return await supabase
      .from('user_profiles')
      .update({
        total_investment: totalInvestment,
        total_profit: totalProfit,
        withdrawal_funds: withdrawalFunds,
      })
      .eq('id', clientId)
      .select()
      .single();
  },
};

export const notificationApi = {
  // Create notification
  createNotification: async (notificationData: {
    user_id: string;
    organization_id: string;
    title: string;
    message: string;
    notification_type: string;
  }) => {
    return await supabase
      .from('notifications')
      .insert(notificationData)
      .select()
      .single();
  },

  // Get notifications for user
  getUserNotifications: async (userId: string) => {
    return await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
  },

  // Mark notification as read
  markAsRead: async (notificationId: string) => {
    return await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);
  },

  // Mark all notifications as read for user
  markAllAsRead: async (userId: string) => {
    return await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId)
      .eq('is_read', false);
  },

  // Get unread count
  getUnreadCount: async (userId: string) => {
    const { count } = await supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    return { data: count, error: null };
  },
};

// =============================================
// USER PROFILE API
// =============================================

export const userProfileApi = {
  updateUserProfile: async (
    userId: string,
    updates: {
      investment_status?: string;
      total_investment?: number;
      total_profit?: number;
      withdrawal_funds?: number;
      monthly_capital?: number;
      quarterly_capital?: number;
      completed_cycles?: number;
      split_cycle_start_date?: string;
      split_cycle_completed_date?: string;
    }
  ) => {
    return supabase
      .from('user_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);
  },
};

// =============================================
// QUARTERLY INVESTMENT TRACKING SYSTEM
// =============================================

export const quarterlyInvestmentApi = {
  // Get quarterly investment details for a user
  getQuarterlyInvestmentDetails: async (userId: string) => {
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status, quarterly_capital, monthly_capital')
      .eq('id', userId)
      .single();

    if (!userProfile) {
      return { data: null, error: { message: 'User profile not found' } };
    }

    // If user is quarterly_only, get their latest balance sheet closing balance
    if (userProfile.investment_status === 'quarterly_only') {
      const { data: latestBalanceSheet } = await supabase
        .from('client_balance_sheets')
        .select('closing_balance, period_month, period_year')
        .eq('client_id', userId)
        .order('period_year', { ascending: false })
        .order('period_month', { ascending: false })
        .limit(1)
        .single();

      return {
        data: {
          investment_status: userProfile.investment_status,
          current_quarterly_capital: latestBalanceSheet?.closing_balance || 0,
          current_monthly_capital: 0,
          last_balance_sheet: latestBalanceSheet,
        },
        error: null,
      };
    }

    return {
      data: {
        investment_status: userProfile.investment_status,
        current_quarterly_capital: userProfile.quarterly_capital || 0,
        current_monthly_capital: userProfile.monthly_capital || 0,
        last_balance_sheet: null,
      },
      error: null,
    };
  },

  // Update quarterly capital based on closing balance for quarterly_only users
  updateQuarterlyCapitalFromClosingBalance: async (
    userId: string,
    organizationId: string
  ) => {
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('investment_status, quarterly_capital')
      .eq('id', userId)
      .single();

    if (!userProfile || userProfile.investment_status !== 'quarterly_only') {
      return { data: null, error: { message: 'User is not quarterly_only' } };
    }

    // Get the latest balance sheet closing balance
    const { data: latestBalanceSheet } = await supabase
      .from('client_balance_sheets')
      .select('closing_balance')
      .eq('client_id', userId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false })
      .limit(1)
      .single();

    if (!latestBalanceSheet) {
      return { data: null, error: { message: 'No balance sheet found' } };
    }

    const newQuarterlyCapital = latestBalanceSheet.closing_balance;
    const capitalDifference =
      newQuarterlyCapital - (userProfile.quarterly_capital || 0);

    // Update user's quarterly capital
    await supabase
      .from('user_profiles')
      .update({
        quarterly_capital: newQuarterlyCapital,
      })
      .eq('id', userId);

    // Update organization quarterly capital
    if (capitalDifference !== 0) {
      await organizationApi.updateQuarterlyCapital(
        organizationId,
        capitalDifference
      );
    }

    return {
      data: {
        old_quarterly_capital: userProfile.quarterly_capital || 0,
        new_quarterly_capital: newQuarterlyCapital,
        capital_difference: capitalDifference,
      },
      error: null,
    };
  },

  // Get quarterly investment history for a user
  getQuarterlyInvestmentHistory: async (userId: string) => {
    const { data: balanceSheets } = await supabase
      .from('client_balance_sheets')
      .select('*')
      .eq('client_id', userId)
      .order('period_year', { ascending: false })
      .order('period_month', { ascending: false });

    if (!balanceSheets) {
      return { data: [], error: null };
    }

    // Transform balance sheets into quarterly investment tracking format
    const quarterlyHistory = balanceSheets.map((sheet, index) => {
      const quarterNumber =
        (sheet.period_year - 2024) * 12 + sheet.period_month;
      const quarterName = `Q${quarterNumber}`;

      return {
        id: sheet.id,
        quarter_name: quarterName,
        period_month: sheet.period_month,
        period_year: sheet.period_year,
        opening_balance: sheet.opening_balance,
        investments_made: sheet.investments_made,
        pnl_earned: sheet.pnl_earned,
        withdrawals_made: sheet.withdrawals_made,
        closing_balance: sheet.closing_balance,
        created_at: sheet.created_at,
        is_latest: index === 0,
      };
    });

    return { data: quarterlyHistory, error: null };
  },
};
