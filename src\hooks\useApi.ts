/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  userApi,
  investmentRequestApi,
  withdrawalRequestApi,
  clientBalanceSheetApi,
  notificationApi,
  organizationApi,
} from '@/lib/api';
import {
  dashboardApi,
  transactionApi,
  adminBalanceSheetApi,
  supabase,
} from '@/lib/supabase';
import { queryKeys, invalidateQueries } from '@/lib/react-query';
import { useToast } from '@/hooks/use-toast';

// =============================================
// TRANSACTION WINDOW HOOKS
// =============================================

export const useActiveTransactionWindow = (organizationId: string) => {
  return useQuery({
    queryKey: ['transaction-window', 'active', organizationId],
    queryFn: () =>
      adminBalanceSheetApi.getActiveTransactionWindow(organizationId),
    enabled: !!organizationId,
    retry: false, // Don't retry if no active window found
  });
};

// Hook for getting active transaction window for a specific user
export const useActiveTransactionWindowForUser = (
  organizationId: string,
  userId: string
) => {
  return useQuery({
    queryKey: ['transaction-window', 'active', 'user', organizationId, userId],
    queryFn: () =>
      adminBalanceSheetApi.getActiveTransactionWindowForUser(
        organizationId,
        userId
      ),
    enabled: !!organizationId && !!userId,
    retry: false, // Don't retry if no active window found
  });
};

export const useAdminBalanceSheets = (organizationId: string) => {
  return useQuery({
    queryKey: ['admin-balance-sheets', organizationId],
    queryFn: () => adminBalanceSheetApi.getAdminBalanceSheets(organizationId),
    enabled: !!organizationId,
  });
};

export const useOpenWindows = (organizationId: string) => {
  return useQuery({
    queryKey: ['open-windows', organizationId],
    queryFn: () => adminBalanceSheetApi.getOpenWindows(organizationId),
    enabled: !!organizationId,
  });
};

// Phase 2: Initial Window Opening Hook
export const useCreateInitialWindow = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: adminBalanceSheetApi.createInitialWindowWithClientSheets,
    onSuccess: (result, variables) => {
      if (result.error) {
        toast({
          title: 'Error',
          description: result.error.message,
          variant: 'destructive',
        });
        return;
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['admin-balance-sheets', variables.organization_id],
      });
      queryClient.invalidateQueries({
        queryKey: ['transaction-window', 'active', variables.organization_id],
      });
      queryClient.invalidateQueries({
        queryKey: ['organization-data', variables.organization_id],
      });

      toast({
        title: 'Initial Window Created',
        description: `Successfully created initial ${variables.period_type} window with client balance sheets`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create initial window',
        variant: 'destructive',
      });
    },
  });
};

// Close Window Hook
export const useCloseWindow = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: adminBalanceSheetApi.closeWindowNormal,
    onSuccess: (_, windowId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['open-windows'],
      });
      queryClient.invalidateQueries({
        queryKey: ['admin-balance-sheets'],
      });
      queryClient.invalidateQueries({
        queryKey: ['transaction-window', 'active'],
      });

      toast({
        title: 'Window Closed',
        description: 'Transaction window has been closed successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to close window',
        variant: 'destructive',
      });
    },
  });
};

// Phase 5: Close Window with Complete Updates Hook
export const useCloseWindowWithUpdates = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: adminBalanceSheetApi.closeWindowWithUpdates,
    onSuccess: (result, windowId) => {
      if (result.error) {
        toast({
          title: 'Error',
          description: result.error.message,
          variant: 'destructive',
        });
        return;
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['open-windows'],
      });
      queryClient.invalidateQueries({
        queryKey: ['admin-balance-sheets'],
      });
      queryClient.invalidateQueries({
        queryKey: ['transaction-window', 'active'],
      });
      queryClient.invalidateQueries({
        queryKey: ['organization-data'],
      });
      queryClient.invalidateQueries({
        queryKey: ['user-profiles'],
      });

      const data = result.data;
      toast({
        title: 'Window Closed Successfully',
        description: `Processed ${
          data?.approvedInvestments?.length || 0
        } investments and ${
          data?.approvedWithdrawals?.length || 0
        } withdrawals. Updated ${
          data?.statusUpdates?.updatedUsers || 0
        } user statuses. Processed capital splitting for ${
          data?.capitalSplittingUpdates?.processedClients || 0
        } clients.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to close window with updates',
        variant: 'destructive',
      });
    },
  });
};

// Phase 6: Create Profit Distribution Hook
export const useCreateProfitDistribution = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: adminBalanceSheetApi.createProfitDistribution,
    onSuccess: (result) => {
      if (result.error) {
        toast({
          title: 'Error',
          description: result.error.message,
          variant: 'destructive',
        });
        return;
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['admin-balance-sheets'],
      });
      queryClient.invalidateQueries({
        queryKey: ['organization-data'],
      });
      queryClient.invalidateQueries({
        queryKey: ['user-profiles'],
      });

      const data = result.data;
      toast({
        title: 'Profit Distribution Created',
        description: `Created profit distribution with ${
          data?.profitCalculations?.profitPercentage || 0
        }% profit rate. Generated ${
          data?.clientBalanceSheets?.updatedClients || 0
        } client balance sheets.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create profit distribution',
        variant: 'destructive',
      });
    },
  });
};

// Phase 7: Quarter Transitions Hook
export const useHandleQuarterTransitions = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      organizationId,
      completedQuarter,
    }: {
      organizationId: string;
      completedQuarter: string;
    }) =>
      adminBalanceSheetApi.handleQuarterTransitions(
        organizationId,
        completedQuarter
      ),
    onSuccess: (result) => {
      if (result.error) {
        toast({
          title: 'Error',
          description: result.error.message,
          variant: 'destructive',
        });
        return;
      }

      const data = result.data;
      toast({
        title: 'Quarter Transition Completed',
        description: `Transferred ${data?.transferredUsers || 0} users from ${
          data?.sourceQuarter
        } to ${data?.targetQuarter}. Total amount: ₹${
          data?.totalAmountTransferred?.toLocaleString() || 0
        }`,
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['user-profiles'],
      });
      queryClient.invalidateQueries({
        queryKey: ['quarterly-positions'],
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to handle quarter transitions',
        variant: 'destructive',
      });
    },
  });
};

// Phase 8: Complete Cycle Hook
export const useCompleteCycle = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      organizationId,
      adminBalanceSheetId,
    }: {
      organizationId: string;
      adminBalanceSheetId: string;
    }) =>
      adminBalanceSheetApi.completeCycle(organizationId, adminBalanceSheetId),
    onSuccess: (result) => {
      if (result.error) {
        toast({
          title: 'Error',
          description: result.error.message,
          variant: 'destructive',
        });
        return;
      }

      const data = result.data;
      toast({
        title: 'Cycle Completed Successfully',
        description: `Completed cycle ${data?.organizationCycles}. Updated ${
          data?.participatingUsers || 0
        } users. ${
          data?.quarterTransitions
            ? `Quarter transitions: ${data.quarterTransitions.transferredUsers} users transferred.`
            : ''
        }`,
      });

      // Invalidate all relevant queries
      queryClient.invalidateQueries({
        queryKey: ['organization-data'],
      });
      queryClient.invalidateQueries({
        queryKey: ['user-profiles'],
      });
      queryClient.invalidateQueries({
        queryKey: ['admin-balance-sheets'],
      });
      queryClient.invalidateQueries({
        queryKey: ['quarterly-positions'],
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to complete cycle',
        variant: 'destructive',
      });
    },
  });
};

// =============================================
// USER PROFILE HOOKS
// =============================================

export const useUserProfile = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.user.profile(userId),
    queryFn: () => userApi.getUserProfile(userId),
    enabled: !!userId,
  });
};

export const useUpdateUserProfile = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ userId, updates }: { userId: string; updates: any }) =>
      userApi.updateUserProfile(userId, updates),
    onSuccess: (data, variables) => {
      invalidateQueries.userProfile(variables.userId);
      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update profile',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateUserFinancials = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ userId, financials }: { userId: string; financials: any }) =>
      userApi.updateUserFinancials(userId, financials),
    onSuccess: (data, variables) => {
      invalidateQueries.userProfile(variables.userId);
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update financial data',
        variant: 'destructive',
      });
    },
  });
};

// =============================================
// INVESTMENT REQUEST HOOKS
// =============================================

export const useInvestmentRequests = (clientId: string) => {
  return useQuery({
    queryKey: queryKeys.investmentRequests.byClient(clientId),
    queryFn: () => investmentRequestApi.getClientRequests(clientId),
    enabled: !!clientId,
  });
};

export const useOrganizationInvestmentRequests = (organizationId: string) => {
  return useQuery({
    queryKey: queryKeys.investmentRequests.byOrganization(organizationId),
    queryFn: () => investmentRequestApi.getRequests(organizationId),
    enabled: !!organizationId,
  });
};

export const useCreateInvestmentRequest = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (variables: {
      client_id: string;
      organization_id: string;
      amount: number;
      payment_method: 'upi' | 'bank_transfer';
    }) => {
      // Check if transaction window is active using admin balance sheets
      const { data: windowData, error: windowError } =
        await adminBalanceSheetApi.getActiveTransactionWindowForUser(
          variables.organization_id,
          variables.client_id
        );
      if (
        windowError &&
        'code' in windowError &&
        windowError.code !== 'PGRST116'
      ) {
        // PGRST116 is "no rows returned"
        throw new Error('Failed to check transaction window status');
      }

      if (!windowData) {
        throw new Error(
          'Investment requests can only be submitted during active transaction windows. Please wait for the next window to open.'
        );
      }

      // Get user's current investment status
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('investment_status')
        .eq('id', variables.client_id)
        .single();

      return investmentRequestApi.createRequest({
        ...variables,
        admin_balance_sheet_id: windowData.id,
        user_status_when_requested:
          userProfile?.investment_status || 'new_month_1',
      });
    },
    onSuccess: (data, variables) => {
      invalidateQueries.investmentRequests(
        variables.client_id,
        variables.organization_id
      );
      toast({
        title: 'Investment Request Submitted',
        description: `Request for ₹${variables.amount.toLocaleString()} submitted for admin approval`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to submit investment request',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateInvestmentRequestStatus = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({
      requestId,
      status,
      adminId,
      adminNotes,
    }: {
      requestId: string;
      status: 'approved' | 'rejected';
      adminId: string;
      adminNotes?: string;
    }) =>
      investmentRequestApi.updateRequestStatus(
        requestId,
        status,
        adminId,
        adminNotes
      ),
    onSuccess: (data) => {
      if (data.data) {
        invalidateQueries.investmentRequests(
          data.data.client_id,
          data.data.organization_id
        );
        toast({
          title: 'Request Updated',
          description: `Investment request has been ${data.data.status}`,
        });
      }
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update investment request',
        variant: 'destructive',
      });
    },
  });
};

// =============================================
// WITHDRAWAL REQUEST HOOKS
// =============================================

export const useWithdrawalRequests = (clientId: string) => {
  return useQuery({
    queryKey: queryKeys.withdrawalRequests.byClient(clientId),
    queryFn: () => withdrawalRequestApi.getClientRequests(clientId),
    enabled: !!clientId,
  });
};

export const useOrganizationWithdrawalRequests = (organizationId: string) => {
  return useQuery({
    queryKey: queryKeys.withdrawalRequests.byOrganization(organizationId),
    queryFn: () => withdrawalRequestApi.getRequests(organizationId),
    enabled: !!organizationId,
  });
};

export const useCreateWithdrawalRequest = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (variables: {
      client_id: string;
      organization_id: string;
      amount: number;
      withdrawal_method: 'upi' | 'bank_transfer';
      upi_id?: string;
      bank_name?: string;
      bank_ifsc?: string;
      bank_account_number?: string;
      bank_account_holder?: string;
    }) => {
      // Check if transaction window is active using admin balance sheets
      const { data: windowData, error: windowError } =
        await adminBalanceSheetApi.getActiveTransactionWindowForUser(
          variables.organization_id,
          variables.client_id
        );

      if (
        windowError &&
        'code' in windowError &&
        windowError.code !== 'PGRST116'
      ) {
        // PGRST116 is "no rows returned"
        throw new Error('Failed to check transaction window status');
      }

      if (!windowData) {
        throw new Error(
          'Withdrawal requests can only be submitted during active transaction windows. Please wait for the next window to open.'
        );
      }

      // Phase 4: Check available funds and user eligibility
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('withdrawal_funds, investment_status')
        .eq('id', variables.client_id)
        .single();

      if (!userProfile) {
        throw new Error('User profile not found');
      }

      // Phase 4: Prevent new_month_1 users from withdrawing
      if (userProfile.investment_status === 'new_month_1') {
        throw new Error(
          'Withdrawals are not allowed for new_month_1 users. Please wait until your status changes to new_month_2.'
        );
      }

      if (userProfile.withdrawal_funds < variables.amount) {
        throw new Error(
          `Insufficient funds. Available: ₹${userProfile.withdrawal_funds.toLocaleString()}`
        );
      }

      return withdrawalRequestApi.createRequest({
        ...variables,
        admin_balance_sheet_id: windowData.id,
      });
    },
    onSuccess: (data, variables) => {
      invalidateQueries.withdrawalRequests(
        variables.client_id,
        variables.organization_id
      );
      toast({
        title: 'Withdrawal Request Submitted',
        description: `Request for ₹${variables.amount.toLocaleString()} submitted for admin approval`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to submit withdrawal request',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateWithdrawalRequestStatus = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({
      requestId,
      status,
      adminId,
      adminNotes,
    }: {
      requestId: string;
      status: 'approved' | 'rejected';
      adminId: string;
      adminNotes?: string;
    }) =>
      withdrawalRequestApi.updateRequestStatus(
        requestId,
        status,
        adminId,
        adminNotes
      ),
    onSuccess: (data) => {
      if (data.data) {
        invalidateQueries.withdrawalRequests(
          data.data.client_id,
          data.data.organization_id
        );
        toast({
          title: 'Request Updated',
          description: `Withdrawal request has been ${data.data.status}`,
        });
      }
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update withdrawal request',
        variant: 'destructive',
      });
    },
  });
};

// =============================================
// BALANCE SHEET HOOKS
// =============================================

export const useClientBalanceSheets = (clientId: string) => {
  return useQuery({
    queryKey: queryKeys.balanceSheets.byClient(clientId),
    queryFn: () => clientBalanceSheetApi.getClientBalanceSheets(clientId),
    enabled: !!clientId,
  });
};

export const useLatestBalanceSheet = (clientId: string) => {
  return useQuery({
    queryKey: queryKeys.balanceSheets.latest(clientId),
    queryFn: () => clientBalanceSheetApi.getLatestBalanceSheet(clientId),
    enabled: !!clientId,
  });
};

// =============================================
// DASHBOARD HOOKS
// =============================================

export const useDashboardOverview = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.dashboard.overview(userId),
    queryFn: () => dashboardApi.getClientOverview(userId),
    enabled: !!userId,
  });
};

export const useUserTransactions = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.dashboard.transactions(userId),
    queryFn: () => transactionApi.getUserTransactions(userId),
    enabled: !!userId,
  });
};

// =============================================
// NOTIFICATIONS HOOKS
// =============================================

export const useNotifications = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.notifications.byUser(userId),
    queryFn: () => notificationApi.getUserNotifications(userId),
    enabled: !!userId,
  });
};

export const useUnreadNotificationsCount = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.notifications.unreadCount(userId),
    queryFn: () => notificationApi.getUnreadCount(userId),
    enabled: !!userId,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

// =============================================
// ADMIN-SPECIFIC HOOKS
// =============================================

export const useOrganizationClients = (organizationId: string) => {
  return useQuery({
    queryKey: queryKeys.user.clients(organizationId),
    queryFn: () => userApi.getClients(organizationId),
    enabled: !!organizationId,
  });
};

export const useOrganizationData = (organizationId: string) => {
  return useQuery({
    queryKey: queryKeys.organizations.byId(organizationId),
    queryFn: () => organizationApi.getOrganization(organizationId),
    enabled: !!organizationId,
  });
};

export const useCreateClient = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.createClient,
    onSuccess: (data, variables) => {
      // Invalidate clients list for the organization
      queryClient.invalidateQueries({
        queryKey: queryKeys.user.clients(variables.organization_id),
      });
      toast({
        title: 'Client Created',
        description: `Client ${variables.first_name} ${variables.last_name} created successfully`,
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to create client',
        variant: 'destructive',
      });
    },
  });
};

export const useAdminDashboardOverview = (organizationId: string) => {
  return useQuery({
    queryKey: queryKeys.dashboard.adminOverview(organizationId),
    queryFn: () => dashboardApi.getAdminOverview(organizationId),
    enabled: !!organizationId,
  });
};

export const useClientsByInvestmentStatus = (
  organizationId: string,
  investmentStatuses: string[]
) => {
  return useQuery({
    queryKey: queryKeys.user.byInvestmentStatus(
      organizationId,
      investmentStatuses
    ),
    queryFn: () =>
      userApi.getClientsByInvestmentStatus(organizationId, investmentStatuses),
    enabled: !!organizationId && investmentStatuses.length > 0,
  });
};

export const useClientLatestBalanceSheet = (clientId: string) => {
  return useQuery({
    queryKey: queryKeys.balanceSheets.latest(clientId),
    queryFn: () => clientBalanceSheetApi.getLatestBalanceSheet(clientId),
    enabled: !!clientId,
  });
};

export const useCreateBalanceSheet = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: clientBalanceSheetApi.upsertBalanceSheet,
    onSuccess: (_, variables) => {
      // Invalidate balance sheets for the client
      queryClient.invalidateQueries({
        queryKey: queryKeys.balanceSheets.byClient(variables.client_id),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.balanceSheets.latest(variables.client_id),
      });
      toast({
        title: 'Balance Sheet Created',
        description: 'Client balance sheet created successfully',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to create balance sheet',
        variant: 'destructive',
      });
    },
  });
};
