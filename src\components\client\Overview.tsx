/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ArrowUpCircle, ArrowDownCircle, AlertCircle } from 'lucide-react';

interface OverviewProps {
  userProfile: any;
  pendingRequests: any[];
  withdrawAmount: string;
  setWithdrawAmount: (value: string) => void;
  investAmount: string;
  setInvestAmount: (value: string) => void;
  handleWithdrawRequest: () => void;
  handleInvestRequest: () => void;
  currentQuarter: string;
  activeWindow: any;
}

const Overview: React.FC<OverviewProps> = ({
  userProfile,
  pendingRequests,
  withdrawAmount,
  setWithdrawAmount,
  investAmount,
  setInvestAmount,
  handleWithdrawRequest,
  handleInvestRequest,
  currentQuarter,
  activeWindow,
}) => {
  const totalInvestment = userProfile?.total_investment || 0;
  const totalProfit = userProfile?.total_profit || 0;
  const withdrawalFunds = userProfile?.withdrawal_funds || 0;
  const monthlyCapital = userProfile?.monthly_capital || 0;
  const quarterlyCapital = userProfile?.quarterly_capital || 0;
  const currentBalance = totalInvestment + totalProfit;
  const isWindowActive = !!activeWindow;

  return (
    <div className="space-y-6">
      {/* Transaction Window Status */}
      {!isWindowActive && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800 dark:text-orange-200">
                  Transaction Window Closed
                </p>
                <p className="text-sm text-orange-600 dark:text-orange-300">
                  Investment and withdrawal requests can only be submitted
                  during active transaction windows. Please wait for the next
                  window to open after profit distribution.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isWindowActive && activeWindow && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <div>
                <p className="font-medium text-green-800 dark:text-green-200">
                  {activeWindow.period_type === 'monthly'
                    ? 'Monthly'
                    : 'Quarterly'}{' '}
                  Transaction Window Active
                </p>
                <p className="text-sm text-green-600 dark:text-green-300">
                  You can submit investment and withdrawal requests until{' '}
                  {new Date(activeWindow.window_end_date).toLocaleDateString()}.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Portfolio Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <Card className="bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-500/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-blue-600">
              Current Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              ₹{currentBalance.toLocaleString()}
            </p>
            <p className="text-sm text-muted-foreground">
              Total Investment + Total Profit
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500/10 to-green-600/10 border-green-500/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-green-600">
              Withdrawal Funds
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              ₹{withdrawalFunds.toLocaleString()}
            </p>
            <p className="text-sm text-muted-foreground">
              Available for withdrawal
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Investment Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Investment Breakdown</CardTitle>
          <CardDescription>
            Your capital allocation across different cycles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Monthly Capital
                  </p>
                  <p className="text-xs text-blue-600 dark:text-blue-300">
                    Active in monthly cycles
                  </p>
                </div>
                <p className="text-lg font-bold text-blue-600">
                  ₹{monthlyCapital.toLocaleString()}
                </p>
              </div>

              <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                    Quarterly Capital
                  </p>
                  <p className="text-xs text-purple-600 dark:text-purple-300">
                    Active in quarterly cycles
                  </p>
                </div>
                <p className="text-lg font-bold text-purple-600">
                  ₹{quarterlyCapital.toLocaleString()}
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-950/20 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    Total Investment
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-300">
                    All approved investments
                  </p>
                </div>
                <p className="text-lg font-bold text-gray-600">
                  ₹{totalInvestment.toLocaleString()}
                </p>
              </div>

              <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    Total Profit
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-300">
                    Profit earned to date
                  </p>
                </div>
                <p className="text-lg font-bold text-green-600">
                  +₹{totalProfit.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pending Requests */}
      {pendingRequests.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader>
            <CardTitle className="text-yellow-600">Pending Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {pendingRequests.map((request) => (
                <div
                  key={request.id}
                  className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg"
                >
                  <div>
                    <p className="font-medium capitalize">
                      {request.request_type} Request
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(request.created_at).toLocaleDateString()} •{' '}
                      {request.quarter?.toUpperCase() || 'N/A'}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">
                      ₹{request.amount.toLocaleString()}
                    </p>
                    <Badge variant="outline">Pending</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-4">
        <Dialog>
          <DialogTrigger asChild>
            <Button
              className="w-full"
              variant="outline"
              disabled={!isWindowActive}
            >
              <ArrowUpCircle className="h-4 w-4 mr-2" />
              Invest
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>New Investment Request</DialogTitle>
              <DialogDescription>
                Submit a request for a new investment to your portfolio
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="invest-amount">Investment Amount (₹)</Label>
                <Input
                  id="invest-amount"
                  type="number"
                  placeholder="Enter amount"
                  value={investAmount}
                  onChange={(e) => setInvestAmount(e.target.value)}
                />
              </div>
              <Button onClick={handleInvestRequest} className="w-full">
                Submit Investment Request
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        <Dialog>
          <DialogTrigger asChild>
            <Button
              className="w-full"
              variant="outline"
              disabled={!isWindowActive}
            >
              <ArrowDownCircle className="h-4 w-4 mr-2" />
              Withdraw
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>New Withdrawal Request</DialogTitle>
              <DialogDescription>
                Submit a request to withdraw funds from your portfolio
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="withdraw-amount">Withdrawal Amount (₹)</Label>
                <Input
                  id="withdraw-amount"
                  type="number"
                  placeholder="Enter amount"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Available: ₹{withdrawalFunds.toLocaleString()}
                </p>
              </div>
              <Button onClick={handleWithdrawRequest} className="w-full">
                Submit Withdrawal Request
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Overview;
